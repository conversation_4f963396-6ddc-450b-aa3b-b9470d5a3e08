openapi: 3.0.1
info:
  version: 1.0.7
  title: Core calculation tool api
  description: This is the Core Calculation(CoCa) tool YAML configuration
  contact:
    name: DevOps Factory
servers:
  - url: http://localhost:8080
    description: Generated server url
security:
  - BEARER_JWT: [ ]
tags:
  - name: User
    description: All User related methods.
  - name: Filters
    description: All Filters related methods.
  - name: Quotation
    description: All Quotation related methods.
  - name: Material Pricing
    description: All Material Pricing related methods.
  - name: Labour Pricing
    description: All Labour Pricing related methods.
  - name: Subcontract Pricing
    description: All Subcontract Pricing related methods.
  - name: Repair Exclusions
    description: All Repair Exclusions related methods.
  - name: Modular Pricing
    description: All Modular Pricing related methods.
  - name: Escalation Pricing
    description: All Escalation Pricing related methods.
  - name: Workscope Summary
    description: All Workscope Summary related methods.
  - name: Export
    description: All Export related methods.
  - name: Exchange Rate
    description: All Exchange Rate related methods.
  - name: Calculation State
    description: All Calculation State related methods.
  - name: Copy State
    description: All Copy State related methods.
  - name: Epar Prices
    description: All Epar Prices related methods.
  - name: Conditional Tasks
    description: All Conditional Tasks related methods.
  - name: User Data
    description: All User Data related methods.
  - name: Engine
    description: All Engine related methods.
  - name: Discounts
    description: All Discounts related methods.
  - name: Surcharges
    description: All Surcharges related methods.
  - name: Third Party Credit
    description: All Third Party Credit related methods.
paths:
  /users:
    get:
      description: This method returns all the possible users that quotation can be assigned
      tags:
        - User
      operationId: getAllUsers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationOwnerResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /users/details:
    put:
      tags:
        - User
      operationId: getOrCreateUserDetails
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /filters/quotations:
    get:
      description: This method returns all the possible filter options
      tags:
        - Filters
      operationId: getQuotationFilters
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationFiltersResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /filters/offer-numbers:
    get:
      description: This method returns a list of existing offer numbers depending of the applied filters
      tags:
        - Filters
      operationId: getOfferNumbers
      parameters:
        - name: requestParameters
          in: query
          schema:
            $ref: "#/components/schemas/OfferNumbersQueryParameters"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AutoCompleteOfferNumberResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations:
    get:
      description: This method returns a list of quotations with pagination and filtering applied
      tags:
        - Quotation
      operationId: getQuotations
      parameters:
        - name: quotationRequestParameters
          in: query
          schema:
            $ref: "#/components/schemas/QuotationsQueryParameters"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationsPageResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{originalQuotationId}/owner:
    patch:
      tags:
        - Quotation
      description: This method changes a quotation's and it's copies' owner by a given quotation ID.
      operationId: changeOwner
      parameters:
        - name: originalQuotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
            description: The quotation ID of a given quotation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeOwnerRequest"
      responses:
        "200":
          description: OK
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{originalQuotationId}/copy:
    put:
      description: This endpoint initiates the quotation process by copying the specified original quotation.
      tags:
        - Quotation
      operationId: copyQuotation
      parameters:
        - name: originalQuotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/archive:
    put:
      description: This endpoint archives a quotation based on its ID
      tags:
        - Quotation
      operationId: archiveQuotation
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: OK
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/unarchive:
    get:
      description: Checks if unarchiving a new quotation is allowed based on a system limit of 2 unarchived quotations.
      tags:
        - Quotation
      operationId: getUnarchiveEligibility
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationCanUnarchiveResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/unarchive:
    put:
      description: This endpoint unarchives a quotation and all copies based on its ID
      tags:
        - Quotation
      operationId: unarchiveQuotation
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: OK
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}:
    get:
      description: This method returns current quotation details
      tags:
        - Quotation
      operationId: getQuotationDetails
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the quotation's contract types if not saved and initiates the quotation process
      tags:
        - Quotation
      operationId: beginQuotation
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BeginQuotationRequest"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/versions:
    get:
      description: This method returns all quotation versions
      tags:
        - Quotation
      operationId: getQuotationVersions
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationVersionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/comment:
    put:
      description: This method creates or updates the comment for the given quotation and returns it.
      tags:
        - Quotation
      operationId: saveComment
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CommentRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/handling-charges:
    get:
      description: This method returns current quotation handling charges
      tags:
        - Material Pricing
      operationId: getHandlingCharges
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HandlingChargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method returns current quotation handling charges
      tags:
        - Material Pricing
      operationId: saveHandlingCharges
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/HandlingChargesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HandlingChargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/usm-ratings:
    get:
      description: This method returns current quotation usm ratings
      tags:
        - Material Pricing
      operationId: getZ2Ratings
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Z2RatingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method save and returns current quotation usm ratings
      tags:
        - Material Pricing
      operationId: saveZ2Ratings
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Z2RatingsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Z2RatingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/scrap-caps:
    get:
      description: This method returns current quotation Scrap Caps data
      tags:
        - Material Pricing
      operationId: getScrapCaps
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScrapCapsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation Scrap Caps data
      tags:
        - Material Pricing
      operationId: saveScrapCaps
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ScrapCapsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScrapCapsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/commitment-letter:
    get:
      description: This method returns the Commitment Letter for the quotation
      tags:
        - Material Pricing
      operationId: getCommitmentLetter
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommitmentLetterResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the Commitment Letter for the quotation
      tags:
        - Material Pricing
      operationId: saveCommitmentLetter
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CommitmentLetterRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommitmentLetterResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/pma-ratings:
    get:
      description: This method returns current quotation PMA ratings
      tags:
        - Material Pricing
      operationId: getPmaRatings
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PmaRatingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation PMA ratings
      tags:
        - Material Pricing
      operationId: savePmaRatings
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PmaRatingsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PmaRatingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/labour-rates:
    get:
      description: This method returns current labour rates saved inputs and user progress for this quotation
      tags:
        - Labour Pricing
      operationId: getLabourRates
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation labour rates
      tags:
        - Labour Pricing
      operationId: saveLabourRates
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LabourRateInput"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/rlfp-engine:
    get:
      description: This method returns current RLFP engine info related to current quotation and user progress for this quotation
      tags:
        - Labour Pricing
      operationId: getRfpEngineData
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRfpEngineResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation RLFP engine data
      tags:
        - Labour Pricing
      operationId: saveRfpEngineData
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RfpRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRfpEngineResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/rlfp-module:
    get:
      description: This method returns current RLFP Module info related to current quotation and user progress for this quotation
      tags:
        - Labour Pricing
      operationId: getRfpModule
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RfpModuleResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation RLFP Module data
      tags:
        - Labour Pricing
      operationId: saveRfpModule
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RfpRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RfpModuleResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/subcontracts:
    get:
      description: This method returns current Subcontract Pricing info related to current quotation and user progress for this quotation
      tags:
        - Subcontract Pricing
      operationId: getSubcontractPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method returns current Subcontract Pricing info related to current quotation and user progress for this quotation
      tags:
        - Subcontract Pricing
      operationId: saveSubcontractPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubcontractPricingRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/repair-exclusions:
    get:
      description: This method returns current quotation Repair Exclusions data
      tags:
        - Repair Exclusions
      operationId: getRepairExclusions
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RepairExclusionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation Repair Exclusions data
      tags:
        - Repair Exclusions
      operationId: saveRepairExclusions
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RepairExclusionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RepairExclusionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/modular-pricing:
    get:
      description: This method returns the Modular Pricing for the quotation
      tags:
        - Modular Pricing
      operationId: getModularPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ModularPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the Modular Pricing for the quotation
      tags:
        - Modular Pricing
      operationId: saveModularPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ModularPricingRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ModularPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/escalations:
    get:
      description: This method returns current Escalations Pricing info related to current quotation and user progress for this quotation
      tags:
        - Escalation Pricing
      operationId: getEscalationPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EscalationsPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method updates the Escalations Pricing for current quotation
      tags:
        - Escalation Pricing
      operationId: saveEscalationPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EscalationPricingRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EscalationsPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/summary:
    get:
      description: This method returns the Workscope Summary for the quotation
      tags:
        - Workscope Summary
      operationId: getWorkscopeSummary
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkscopeSummaryResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    post:
      description: This method begins quotation workscope summary calculations if the user has finished all the previous steps
      tags:
        - Workscope Summary
      operationId: beginCalculations
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: The request has been accepted for processing, and the calculation is underway.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AsyncCalculationStateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the Revenue Cap for the quotation only if NTE/Workscope fixed price selected
      tags:
        - Workscope Summary
      operationId: saveRevenueCap
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RevenueCapRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkscopeSummaryResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/calculation-state:
    get:
      description: This method gets quotation workscope summary calculations state
      tags:
        - Calculation State
      operationId: getCalculationState
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProgressStateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/copy-state/{beginQuotationType}:
    get:
      description: This method gets copy quotation data state
      tags:
        - Copy State
      operationId: getCopyState
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
        - name: beginQuotationType
          in: path
          required: true
          schema:
            type: string
            enum: [ "COPY", "UPDATE" ]
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProgressStateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/export/business-plan:
    get:
      description: This method returns an excel export of the Pricing Overview for the Business Plan Tool.
      tags:
        - Export
      operationId: getBPExport
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/vnd.ms-excel:
              schema:
                type: string
                format: binary
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/export/rlfp:
    get:
      description: This method returns an excel export of the RLFP Engine and RLFP Module screens.
      tags:
        - Export
      operationId: getRFPExport
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/vnd.ms-excel:
              schema:
                type: string
                format: binary
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/exchange-rates:
    get:
      description: This method returns the current and previous exchange rates for the quotation
      tags:
        - Exchange Rate
      operationId: getCurrentAndPreviousExchangeRates
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ExchangeRatesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    post:
      description: This method notifies that the user is informed for the exchange rate change for the quotation
      tags:
        - Exchange Rate
      operationId: updateExchangeRate
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ExchangeRatesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/workscope-comments:
    get:
      summary: Get workscope comments for a quotation
      description: Returns all workscope-level comments, organized by modules and submodules, for the given quotation.
      tags:
        - Quotation
      operationId: getWorkscopeComments
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: Workscope comments for the quotation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkscopeCommentsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/epar-prices:
    get:
      description: This method returns the Epar prices
      tags:
        - Epar Prices
      operationId: getEparPrices
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EparPricesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the Epar prices
      tags:
        - Epar Prices
      operationId: saveEparPrices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EparPricesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EparPricesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/conditional-tasks:
    get:
      description: This method returns the Conditional Tasks
      tags:
        - Conditional Tasks
      operationId: getConditionalTasks
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConditionalTasksResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the Conditional Tasks
      tags:
        - Conditional Tasks
      operationId: saveConditionalTasks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConditionalTasksRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConditionalTasksResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/user-data:
    get:
      description: This method returns the User Data
      tags:
        - User Data
      operationId: getUserData
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDataResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the User Data
      tags:
        - User Data
      operationId: saveUserData
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserDataRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDataResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/user-data/{userId}:
    delete:
      tags:
        - User Data
      operationId: deleteUserData
      description: Delete a user by their ID
      parameters:
        - name: userId
          in: path
          required: true
          description: the ID of the user to be deleted
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/engines:
    get:
      description: Returns all engines
      tags:
        - Engine
      operationId: getAllEngines
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EngineResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/engines/{engineId}/clusters-tasks-matrix:
    get:
      description: Returns Clusters Tasks Matrix data
      tags:
        - Engine
      operationId: getClustersTasksMatrix
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClustersTasksMatrixResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      description: Save Clusters Tasks Matrix data
      tags:
        - Engine
      operationId: saveClustersTasksMatrix
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskDataRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClustersTasksMatrixResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/engines/{engineId}/clusters-parts-matrix:
    get:
      description: Returns Clusters Parts Matrix data
      tags:
        - Engine
      operationId: getClustersPartsMatrix
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClustersPartsMatrixResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      description: Save Clusters Parts Matrix data
      tags:
        - Engine
      operationId: saveClustersPartsMatrix
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PartDataRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClustersPartsMatrixResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/engines/{engineId}/subcontract/parts:
    get:
      description: Returns the available Parts without Subcontract Discounts
      tags:
        - Engine
      operationId: getAvailablePartsNames
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AvailablePartsNamesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/material/conditions/non-expendable:
    get:
      description: Returns the Material Non-Expendable Discounts Conditions
      tags:
        - Discounts
      operationId: getMaterialNonExpendableDiscountsConditions
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialNonExpendableDiscountConditionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Material Non-Expendable Discounts Conditions
      tags:
        - Discounts
      operationId: saveMaterialNonExpendableDiscountsConditions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MaterialNonExpendableDiscountConditionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialNonExpendableDiscountConditionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/material/conditions/expendable:
    get:
      description: Returns the Material Expendable Discounts Conditions
      tags:
        - Discounts
      operationId: getMaterialExpendableDiscountsConditions
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialExpendableDiscountConditionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Material Expendable Discounts Conditions
      tags:
        - Discounts
      operationId: saveMaterialExpendableDiscountsConditions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MaterialExpendableDiscountConditionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialExpendableDiscountConditionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/subcontract/conditions:
    get:
      description: Returns the Subcontract Discounts Conditions
      tags:
        - Discounts
      operationId: getSubcontractDiscountsConditions
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractDiscountConditionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Subcontract Discounts Conditions
      tags:
        - Discounts
      operationId: saveSubcontractDiscountsConditions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubcontractDiscountConditionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractDiscountConditionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/subcontract/portions/{engineId}:
    get:
      description: Returns the Subcontract Discounts Portions
      tags:
        - Discounts
      operationId: getSubcontractDiscountsPortions
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractDiscountPortionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Subcontract Discounts Portions
      tags:
        - Discounts
      operationId: saveSubcontractDiscountsPortions
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubcontractDiscountPortionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractDiscountPortionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/material/portions/{engineId}:
    get:
      description: Returns the Material Discounts Portions
      tags:
        - Discounts
      operationId: getMaterialDiscountsPortions
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialDiscountPortionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Material Discounts Portions
      tags:
        - Discounts
      operationId: saveMaterialDiscountsPortions
      parameters:
        - name: engineId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MaterialDiscountPortionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialDiscountPortionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/material/volume-based:
    get:
      description: Returns the Material Volume Based Discounts
      tags:
        - Discounts
      operationId: getMaterialVolumeBasedDiscounts
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialVolumeBasedDiscountsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Material Volume Based Discounts
      tags:
        - Discounts
      operationId: saveMaterialVolumeBasedDiscounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MaterialVolumeBasedDiscountsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialVolumeBasedDiscountsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/discounts/commitment-letter:
    get:
      description: Returns the Commitment Letter Discounts
      tags:
        - Discounts
      operationId: getCommitmentLetterDiscounts
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommitmentLetterDiscountsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Commitment Letter Discounts
      tags:
        - Discounts
      operationId: saveCommitmentLetterDiscounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CommitmentLetterDiscountsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommitmentLetterDiscountsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/escalation-assumptions/material:
    get:
      description: Returns the Material Escalation Assumptions Surcharges
      tags:
        - Surcharges
      operationId: getMaterialEscalationAssumptions
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialEscalationAssumptionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Material Escalation Assumptions Surcharges
      tags:
        - Surcharges
      operationId: saveMaterialEscalationAssumptions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MaterialEscalationAssumptionsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaterialEscalationAssumptionsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/labour:
    get:
      description: Returns the Labour Surcharges
      tags:
        - Surcharges
      operationId: getLabourSurcharges
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourSurchargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Labour Surcharges
      tags:
        - Surcharges
      operationId: saveLabourSurcharges
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LabourSurchargesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourSurchargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/internal-logistic:
    get:
      description: Returns the Internal Logistic Surcharges
      tags:
        - Surcharges
      operationId: getInternalLogistic
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InternalLogisticResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Internal Logistic Surcharges
      tags:
        - Surcharges
      operationId: saveInternalLogistic
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalLogisticRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InternalLogisticResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/miscellaneous:
    get:
      description: Returns the Miscellaneous Surcharges
      tags:
        - Surcharges
      operationId: getMiscellaneousSurcharges
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MiscellaneousSurchargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Miscellaneous Surcharges
      tags:
        - Surcharges
      operationId: saveMiscellaneousSurcharges
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MiscellaneousSurchargesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MiscellaneousSurchargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/loss-of-value:
    get:
      description: Returns the Material Surcharge for Loss of value
      tags:
        - Surcharges
      operationId: getLossOfValue
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LossOfValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Material Surcharge for Loss of value
      tags:
        - Surcharges
      operationId: saveLossOfValue
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LossOfValueRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LossOfValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/leap-licence-fees:
    get:
      description: Returns the LEAP Licence Fees
      tags:
        - Surcharges
      operationId: getLeapLicenceFees
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeapLicenceFeesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the LEAP Licence Fees
      tags:
        - Surcharges
      operationId: saveLeapLicenceFees
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LeapLicenceFeesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeapLicenceFeesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/surcharges/leap-utilization-fees:
    get:
      description: Returns the LEAP Technology Utilization Fees
      tags:
        - Surcharges
      operationId: getLeapUtilizationFees
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeapUtilizationFeesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the LEAP Technology Utilization Fees
      tags:
        - Surcharges
      operationId: saveLeapUtilizationFees
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LeapUtilizationFeesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeapUtilizationFeesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /admin/third-party-credit:
    get:
      description: Returns the Third Party Credit
      tags:
        - Third Party Credit
      operationId: getThirdPartyCredit
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ThirdPartyCreditResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: Save the Third Party Credit
      tags:
        - Third Party Credit
      operationId: saveThirdPartyCredit
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ThirdPartyCreditRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ThirdPartyCreditResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
components:
  schemas:
    QuotationOwner:
      description: User object that represent quotation owner
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Nikolas Barkow
        username:
          type: string
        email:
          type: string
      required:
        - id
    QuotationOwnerResponse:
      description: The response for Quotation Owner request.
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/QuotationOwner"
        error:
          $ref: "#/components/schemas/Error"
    Resource:
      description: This is the resource object
      type: object
      properties:
        resource:
          type: string
    ScopeHolder:
      description: This is the scope holder object
      type: object
      properties:
        create:
          type: boolean
        read:
          type: boolean
        update:
          type: boolean
        delete:
          type: boolean
    Permission:
      description: This is the permission object
      type: object
      properties:
        resource:
          $ref: "#/components/schemas/Resource"
        scopeHolder:
          $ref: "#/components/schemas/ScopeHolder"
    UserDetails:
      type: object
      description: The user information
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        email:
          type: string
        username:
          type: string
        role:
          type: string
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/Permission"
      required:
        - id
    UserDetailsResponse:
      description: The response for User Details request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/UserDetails"
        error:
          $ref: "#/components/schemas/Error"
    QuotationFilters:
      description: This is the object holding the possible filter options
      type: object
      properties:
        owners:
          type: array
          items:
            $ref: "#/components/schemas/QuotationOwner"
        quotationStatuses:
          type: array
          items:
            type: string
          example:
            - ANKA Validated
            - In Progress
            - Transferred
            - Completed
        engineTypes:
          type: array
          items:
            type: string
          example:
            - CFM56-5B
            - V2500
            - GE90
        customers:
          type: array
          items:
            type: string
          example:
            - DLH
            - IWG
            - WZZ
    QuotationFiltersResponse:
      description: The response for Filters request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationFilters"
        error:
          $ref: "#/components/schemas/Error"
    AutoCompleteOfferNumberResponse:
      description: The response for OfferNumbers request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/OfferNumbersContainer"
        error:
          $ref: "#/components/schemas/Error"
    ChangeOwnerRequest:
      description: Change owner request body object.
      properties:
        id:
          type: integer
          format: int64
          minimum: 1
          description: The id of the quotation's new owner
      required:
        - id
    OfferNumbersQueryParameters:
      description: get quotation list query params wrapper
      type: object
      properties:
        ownerId:
          type: integer
          format: int64
          minimum: 1
          description: The owner id of a given quotation
        offerNumber:
          type: string
          description: Part of the offer number of a given quotation
        version:
          type: integer
          minimum: 1
          description: The version of a given quotation
        position:
          type: integer
          minimum: 1
          description: The position of a given quotation
        scenario:
          type: integer
          minimum: 0
          description: The scenario of a given quotation
        quotationStatus:
          $ref: "#/components/schemas/QuotationStatus"
        engineType:
          type: string
          description: The engine type of a given quotation
        customer:
          type: string
          pattern: ^[A-Z0-9]{3}$
          description: The customer of a given quotation
    QuotationsQueryParameters:
      description: get quotation list query params wrapper
      type: object
      properties:
        pageSize:
          type: integer
          default: 20
          minimum: 0
          description: Quotations count in one quotation page
        pageIndex:
          type: integer
          default: 0
          minimum: 0
          description: Quotation page index
        sortBy:
          $ref: "#/components/schemas/QuotationSort"
        ownerId:
          type: integer
          format: int64
          minimum: 1
          description: The owner id of a given quotation
        offerNumber:
          type: string
          pattern: ^[0-9]{7}$
          description: The offer number of a given quotation
        version:
          type: integer
          minimum: 1
          description: The version of a given quotation
        position:
          type: integer
          minimum: 1
          description: The position of a given quotation
        scenario:
          type: integer
          minimum: 0
          description: The scenario of a given quotation
        quotationStatus:
          $ref: "#/components/schemas/QuotationStatus"
        engineType:
          type: string
          description: The engine type of a given quotation
        customer:
          type: string
          pattern: ^[A-Z0-9]{3}$
          description: The customer of a given quotation
    QuotationLight:
      description: Representation of a quotation used in the list view
      type: object
      properties:
        id:
          type: integer
          format: int64
        offerNumber:
          type: string
        version:
          type: integer
        position:
          type: integer
        scenario:
          type: integer
        customer:
          type: string
        engineType:
          type: string
        contractType:
          $ref: "#/components/schemas/ContractType"
        lastUpdate:
          type: integer
          format: int64
        canCurrentUserEdit:
          type: boolean
        owner:
          $ref: "#/components/schemas/QuotationOwner"
        status:
          $ref: "#/components/schemas/QuotationStatus"
        originalQuotationId:
          type: integer
          format: int64
        copyNumber:
          type: integer
        copiedQuotations:
          type: array
          items:
            $ref: "#/components/schemas/QuotationLight"
        comment:
          type: string
          nullable: true
      required:
        - id
        - owner
    QuotationsPage:
      description: Page of quotations with pagination and filtering
      type: object
      properties:
        totalItems:
          type: integer
          format: int64
        totalPages:
          type: integer
        quotations:
          type: array
          items:
            $ref: "#/components/schemas/QuotationLight"
    OfferNumbersContainer:
      description: The container of OfferNumbers autocomplete request.
      type: object
      properties:
        offerNumbers:
          type: array
          items:
            type: string
    QuotationsPageResponse:
      description: The response for Quotation Page request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationsPage"
        error:
          $ref: "#/components/schemas/Error"
    QuotationCustomer:
      description: Representation of a quotation project customer
      type: object
      properties:
        name:
          type: string
          example: LHT
        type:
          type: string
          example: LHT
    QuotationEngine:
      description: Representation of a engine related to the quotation
      type: object
      properties:
        name:
          type: string
          example: CFM56-5B
    QuotationWorkscope:
      description: Representation of a workscope related to the engine of the current quotation
      type: object
      properties:
        name:
          type: string
          example: CPR-LHC10
        class:
          type: string
          example: A
    ProgressStep:
      description: Representation of progress step
      type: object
      properties:
        name:
          $ref: "#/components/schemas/QuotationProgress"
        isValid:
          type: boolean
        position:
          type: integer
          format: int64
      required:
        - id
    NavigationStep:
      description: Representation of the navigation step holding the progress steps
      type: object
      properties:
        name:
          $ref: "#/components/schemas/NavigationSteps"
        progressSteps:
          type: array
          items:
            $ref: "#/components/schemas/ProgressStep"
        areAllProgressStepsValid:
          type: boolean
        position:
          type: integer
          format: int64
      required:
        - id
    Progress:
      description: Representation of the quotation's completion progress
      type: object
      properties:
        navigationSteps:
          type: array
          items:
            $ref: "#/components/schemas/NavigationStep"
    QuotationDetails:
      description: Representation of a quotation details used in the quotation details screen
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        originalQuotationId:
          type: integer
          format: int64
          example: 1
        offerNumber:
          type: string
          example: 5100207
        version:
          type: integer
          example: 1
        position:
          type: integer
          example: 1
        scenario:
          type: integer
          example: 0
        owner:
          $ref: "#/components/schemas/QuotationOwner"
        contractStart:
          type: integer
          format: int64
          example: 1616577123311
        contractEnd:
          type: integer
          format: int64
          example: 1616577123311
        customer:
          $ref: "#/components/schemas/QuotationCustomer"
        engine:
          $ref: "#/components/schemas/QuotationEngine"
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/QuotationWorkscope"
        lastUpdate:
          type: integer
          format: int64
          example: 1616577123311
        progress:
          $ref: "#/components/schemas/Progress"
        status:
          $ref: "#/components/schemas/QuotationStatus"
        usdExchangeRate:
          type: number
          format: number
          example: 1.07
        timeAndMaterial:
          type: boolean
          default: true
        routineFixedPrices:
          type: boolean
          default: false
        workscopeNte:
          type: boolean
          default: false
        workscopeFixedPrice:
          type: boolean
          default: false
        modularNte:
          type: boolean
          default: false
        modularFixedPrice:
          type: boolean
          default: false
        comment:
          type: string
          nullable: true
        canCurrentUserEdit:
          type: boolean
      required:
        - id
    QuotationDetailsResponse:
      description: The response for Quotation Details request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationDetails"
        error:
          $ref: "#/components/schemas/Error"
    QuotationCanUnarchiveResponse:
      description: Represents a wrapper for the unarchive permission value
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationCanUnarchive"
        error:
          $ref: "#/components/schemas/Error"
    QuotationCanUnarchive:
      description: Represents the boolean value for unarchive permission
      type: object
      properties:
        canUnarchive:
          type: boolean
          description: True if the user is allowed to unarchive, false otherwise
    BeginQuotationRequest:
      description: Begin quotation request body object
      type: object
      properties:
        routineFixedPrices:
          type: boolean
        workscopeNte:
          type: boolean
        workscopeFixedPrice:
          type: boolean
        modularNte:
          type: boolean
        modularFixedPrice:
          type: boolean
        sourceQuotationId:
          type: integer
          format: int64
        beginQuotationType:
          $ref: "#/components/schemas/BeginQuotationType"
      required:
        - routineFixedPrices
        - beginQuotationType
    QuotationVersionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/QuotationDetails"
        error:
          $ref: "#/components/schemas/Error"
    HandlingChargesValues:
      description: Representation of a wrapper for the handling charges values
      type: object
      properties:
        z1:
          type: number
          format: float
          minimum: 0
          nullable: true
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Z1 margin must be less than 100 and with up to one digit after decimal point")'
        z2:
          type: number
          format: float
          minimum: 0
          nullable: true
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Usm margin must be less than 100 and with up to one digit after decimal point")'
        pma:
          type: number
          format: float
          minimum: 0
          nullable: true
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Pma margin must be less than 100 and with up to one digit after decimal point")'
        csm:
          type: number
          format: float
          minimum: 0
          nullable: true
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Csm margin must be less than 100 and with up to one digit after decimal point")'
        oneItemCap:
          type: integer
          minimum: 1
          nullable: true
        lineItemCap:
          type: integer
          minimum: 1
          nullable: true
    HandlingChargesGlobalValues:
      description: Representation of a global values wrapper applied for all parts in handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        baseCase:
          type: number
          format: float
          nullable: true
        globalCap:
          type: integer
          format: int32
          minimum: 1
          nullable: true
    HandlingChargesPart:
      description: Representation of a part for an specific engine related in usage for handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: M1 - Blade LPC Stg.  1 (Fan Blade)
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        quantity:
          type: integer
          example: 1
      required:
        - id
    HandlingChargesCluster:
      description: Representation of a parts' cluster for an specific engine related in usage for handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Fan Blade
        order:
          type: integer
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesPart"
      required:
        - id
    HandlingCharges:
      description: Representation of a quotation handling charges schema
      type: object
      properties:
        globalValue:
          $ref: "#/components/schemas/HandlingChargesGlobalValues"
        canCurrentUserEdit:
          type: boolean
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesCluster"
        progress:
          $ref: "#/components/schemas/Progress"
    HandlingChargesResponse:
      description: The response for Handling Charges request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/HandlingCharges"
        error:
          $ref: "#/components/schemas/Error"
    HandlingChargesPartRequest:
      description: Representation of a part for an specific engine related in usage for handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
      required:
        - id
    HandlingChargesClusterRequest:
      description: Representation of a parts cluster for an specific engine related in usage for handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesPartRequest"
      required:
        - id
        - parts
    HandlingChargesRequest:
      description: Representation of a quotation's handling charges request body
      type: object
      properties:
        globalCap:
          type: integer
          format: int32
          minimum: 1
          nullable: true
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesClusterRequest"
      required:
        - clusters
    Z2Part:
      description: Representation of a quotation z2 ratings data for part
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        currency:
          $ref: "#/components/schemas/Currency"
        clp:
          type: number
          format: double
        clpPercentage:
          type: number
          format: float
        z2Cost:
          type: number
          format: double
        z2RatingInput:
          type: number
          format: float
        quantity:
          type: integer
      required:
        - id
    Z2Cluster:
      description: Representation of a quotation z2 ratings data per cluster
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        order:
          type: integer
        z2RatingInput:
          type: number
          format: float
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/Z2Part"
      required:
        - id
    Z2RatingYear:
      description: Representation of a quotation z2 ratings data per year
      properties:
        year:
          type: string
          minimum: 4
          maximum: 4
        z2RatingInput:
          type: number
          format: float
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/Z2Cluster"
    Z2Ratings:
      description: Representation of a quotation z2 ratings data
      type: object
      properties:
        globalZ2RatingInput:
          type: number
          format: float
        canCurrentUserEdit:
          type: boolean
        years:
          type: array
          items:
            $ref: "#/components/schemas/Z2RatingYear"
        progress:
          $ref: "#/components/schemas/Progress"
    Z2RatingsResponse:
      description: The response for Z2 Ratings request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/Z2Ratings"
        error:
          $ref: "#/components/schemas/Error"
    Z2ratingsInput:
      description: z2 ratings user input data
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
        z2RatingInput:
          type: number
          format: float
          minimum: 0
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Usm rating must be less than 100 and with up to one digit after decimal point")'
    Z2RatingsRequest:
      description: Representation of a quotation's z2 ratings request body
      type: object
      properties:
        z2ratingsInput:
          type: array
          items:
            $ref: "#/components/schemas/Z2ratingsInput"
      required:
        - z2ratingsInput
    ScrapCapsResponse:
      description: The response for Scrap Caps request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/ScrapCaps"
        error:
          $ref: "#/components/schemas/Error"
    ScrapCaps:
      description: Representation of a quotation Scrap Caps schema
      type: object
      properties:
        canCurrentUserEdit:
          type: boolean
        clpThreshold:
          type: integer
          example: 50000
          nullable: true
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/ScrapCapsWorkscope"
        progress:
          $ref: "#/components/schemas/Progress"
    ScrapCapsWorkscope:
      description: Representation of a Scrap Caps per workscope
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: PVHPT-LHCN
        isSystem:
          type: boolean
          example: false
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/ScrapCapsCluster"
        realisticWsId:
          type: integer
          format: int64
          example: 1
          nullable: true
      required:
        - id
        - name
        - isSystem
        - clusters
        - realisticWsId
    ScrapCapsCluster:
      description: Representation of a part's cluster for an specific engine related in usage for Scrap Caps
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Fan Blade
        order:
          type: integer
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/ScrapCapsPart"
      required:
        - id
        - parts
    ScrapCapsPart:
      description: Representation of a part for an specific engine related in usage for Scrap Caps
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: M1 - Blade LPC Stg.  1 (Fan Blade)
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        quantity:
          type: integer
          example: 1
        clp:
          type: number
          format: double
          example: 69030
        currency:
          $ref: "#/components/schemas/Currency"
        ankaScrap:
          type: number
          format: float
          example: 5
        removalRate:
          type: number
          format: float
          example: 0.5
        scrapCapInput:
          type: number
          format: float
          example: 3
        ankaCost:
          type: number
          format: float
          example: 72030
        revenue:
          type: number
          format: float
          example: 69030
        isScrapCapOverwritten:
          type: boolean
        modularWs:
          type: string
          example: L1
        partWs:
          type: string
          example: L2
      required:
        - id
    ScrapCapsRequest:
      description: Representation of a quotation Scrap Caps request body
      type: object
      properties:
        clpThreshold:
          type: integer
          example: 50000
          minimum: 1
          nullable: true
        scrapCapsInput:
          type: array
          items:
            $ref: "#/components/schemas/ScrapCapsInput"
      required:
        - scrapCapsInput
    ScrapCapsInput:
      description: Scrap caps user input data per specific part type
      type: object
      properties:
        id:
          type: integer
          format: int64
        isScrapCapOverwritten:
          type: boolean  
        scrapCapInput:
          type: number
          format: float
          example: 3
          minimum: 0
          maximum: 100
          nullable: true
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 3, fraction = 1, 
                              message = "Scrap cap must be a whole number or a float with up to one digit after decimal point")'
      required:
        - id
    CommitmentLetterPart:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        type:
          $ref: "#/components/schemas/PartType"
        hasCommitmentLetter:
          type: boolean
      required:
        - id
        - name
        - type
        - hasCommitmentLetter
    CommitmentLetter:
      type: object
      properties:
        canCurrentUserEdit:
          type: boolean
        parts:
          type: array
          items:
            $ref: '#/components/schemas/CommitmentLetterPart'
        progress:
          $ref: "#/components/schemas/Progress"
    CommitmentLetterResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CommitmentLetter'
        error:
          $ref: '#/components/schemas/Error'
    CommitmentLetterRequest:
      type: object
      properties:
        parts:
          type: array
          items:
            $ref: '#/components/schemas/CommitmentLetterPart'
    PmaPart:
      description: Representation of a quotation PMA ratings data for part
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        currency:
          $ref: "#/components/schemas/Currency"
        clp:
          type: number
          format: double
        clpPercentage:
          type: number
          format: float
        pmaCost:
          type: number
          format: double
        pmaRatingInput:
          type: number
          format: float
        quantity:
          type: integer
      required:
        - id
    PmaCluster:
      description: Representation of a quotation PMA ratings data per cluster
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        order:
          type: integer
        pmaRatingInput:
          type: number
          format: float
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/PmaPart"
      required:
        - id
    PmaRatingYear:
      description: Representation of a quotation PMA ratings data per year
      properties:
        year:
          type: string
          minimum: 4
          maximum: 4
        pmaRatingInput:
          type: number
          format: float
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/PmaCluster"
    PmaRatings:
      description: Representation of a quotation PMA ratings data
      type: object
      properties:
        globalPmaRatingInput:
          type: number
          format: float
        canCurrentUserEdit:
          type: boolean
        years:
          type: array
          items:
            $ref: "#/components/schemas/PmaRatingYear"
        progress:
          $ref: "#/components/schemas/Progress"
    PmaRatingsResponse:
      description: The response for PMA Ratings request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/PmaRatings"
        error:
          $ref: "#/components/schemas/Error"
    PmaRatingsInput:
      description: PMA ratings user input data
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
        pmaRatingInput:
          type: number
          format: float
          minimum: 0
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Pma rating must be less than 100 and with up to one digit after decimal point")'
    PmaRatingsRequest:
      description: Representation of a quotation's PMA ratings request body
      type: object
      properties:
        pmaRatingsInput:
          type: array
          items:
            $ref: "#/components/schemas/PmaRatingsInput"
      required:
        - pmaRatingsInput
    LabourRateInput:
      description: General Labour Rates input wrapper.
      type: object
      properties:
        routineLabourRate:
          type: integer
        nonRoutineLabourRate:
          type: integer
        eparDiscount:
          type: number
          format: double
          minimum: 0
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 2, fraction = 1, 
          message = "Epar discount must be less than 100 and with up to one digit after decimal point")'
    CiAndRepairRateBaseYear:
      description: CI and Repair Rate values for quotation base year
      type: object
      properties:
        ciRate:
          type: number
          format: double
        repairRate:
          type: number
          format: double
    LabourRate:
      description: Representation labour rate wrapper for quotation
      type: object
      properties:
        labourRate:
          $ref: "#/components/schemas/LabourRateInput"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
        ciAndRepairRateBaseYear:
          $ref: "#/components/schemas/CiAndRepairRateBaseYear"
    LabourRateResponse:
      description: The response for General Labour Rate request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/LabourRate"
        error:
          $ref: "#/components/schemas/Error"
    RfpItemInput:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        price:
          type: number
          format: double
          example: 9999.99
        dbii:
          type: number
          format: double
          example: 15.9
          maximum: 100
          exclusiveMaximum: true
        isTestrunMaterial:
          type: boolean
          example: false
      required:
        - id
    RfpItem:
      type: object
      allOf:
        - $ref: "#/components/schemas/RfpItemInput"
      properties:
        name:
          type: string
        cost:
          type: number
          format: double
        ciHoursCost:
          type: number
          format: double
        order:
          type: integer
    RfpEngineData:
      description: Representation of a quotation rfp engine data
      type: object
      properties:
        ciIncluded:
          type: boolean
        rfpEngineItems:
          type: array
          items:
            $ref: "#/components/schemas/RfpItem"
    RfpEngine:
      description: Representation of a quotation rfp engine data
      type: object
      properties:
        rfpEngineData:
          $ref: "#/components/schemas/RfpEngineData"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    LabourRfpEngineResponse:
      description: The response for Rfp Engine data request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/RfpEngine"
        error:
          $ref: "#/components/schemas/Error"
    RfpModuleWorkscope:
      description: Representation of a RFP Module workscope
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        rfpItems:
          type: array
          items:
            $ref: "#/components/schemas/RfpItem"
      required:
        - id
    RfpModuleData:
      description: Representation of a quotation RFP Module data
      type: object
      properties:
        isCiIncluded:
          type: boolean
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/RfpModuleWorkscope"
    RfpModule:
      description: Representation of a quotation RFP Module
      type: object
      properties:
        rfpModuleData:
          $ref: "#/components/schemas/RfpModuleData"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    RfpModuleResponse:
      description: The response for RFP Module request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/RfpModule"
        error:
          $ref: "#/components/schemas/Error"
    RfpRequest:
      description: Representation of a quotation RFP Engine and RFP Module input data
      type: object
      properties:
        ciIncluded:
          type: boolean
          example: false
        rfpItemInputs:
          type: array
          items:
            $ref: "#/components/schemas/RfpItemInput"
    SubcontractValue:
      description: Representation of a quotation subcontract values
      type: object
      properties:
        margin:
          type: number
          format: float
          nullable: true
          example: 15.9
        cap:
          type: integer
          example: 1000
          nullable: true
    Subcontract:
      description: Representation of a quotation subcontract
      type: object
      allOf:
        - $ref: "#/components/schemas/SubcontractValue"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Fan Blade
        order:
          type: integer
          example: 2
      required:
        - id
    SubcontractPricing:
      description: Representation of a quotation Subcontract Pricing
      type: object
      properties:
        globalCap:
          type: integer
          example: 1000
          nullable: true
        globalValues:
          $ref: "#/components/schemas/SubcontractValue"
        subcontracts:
          type: array
          items:
            $ref: "#/components/schemas/Subcontract"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    SubcontractPricingResponse:
      description: The response for Subcontract Pricing request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/SubcontractPricing"
        error:
          $ref: "#/components/schemas/Error"
    SubcontractInput:
      description: Representation of a quotation subcontract input
      type: object
      allOf:
        - $ref: "#/components/schemas/SubcontractValue"
      properties:
        id:
          type: integer
          format: int64
      required:
        - id
    SubcontractPricingRequest:
      description: Representation of a quotation Subcontract Pricing input data
      type: object
      properties:
        globalCap:
          type: integer
          format: int32
          minimum: 1
          nullable: true
        subcontractInputs:
          type: array
          items:
            $ref: "#/components/schemas/SubcontractInput"
    RepairExclusionsResponse:
      description: The response for Repair Exclusions request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/RepairExclusions"
        error:
          $ref: "#/components/schemas/Error"
    RepairExclusions:
      description: Representation of a quotation Repair Exclusions schema
      type: object
      properties:
        canCurrentUserEdit:
          type: boolean
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/RepairExclusionsWorkscope"
        progress:
          $ref: "#/components/schemas/Progress"
    RepairExclusionsWorkscope:
      description: Representation of a Repair Exclusions per workscope
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: PVHPT-LHCN
        isSystem:
          type: boolean
          example: false
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/RepairExclusionsCluster"
        realisticWsId:
          type: integer
          format: int64
          example: 1
          nullable: true
      required:
        - id
        - clusters
        - isSystem
        - realisticWsId
    RepairExclusionsCluster:
      description: Representation of a part's cluster for an specific engine related in usage for Repair Exclusions
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Fan Blade
        order:
          type: integer
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/RepairExclusionsPart"
      required:
        - id
        - parts
    RepairExclusionsPart:
      description: Representation of a part for a specific engine related in usage for Repair Exclusions
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: M1 - Blade LPC Stg.  1 (Fan Blade)
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        repairs:
          type: array
          items:
            $ref: "#/components/schemas/Repair"
      required:
        - id
        - repairs
    Repair:
      description: Representation of a repair for a specific part
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        workscopeId:
          type: integer
          format: int64
        clusterId:
          type: integer
          format: int64
        partId:
          type: integer
          format: int64
        name:
          type: string
          example: ø-Inhouse Repair
        order:
          type: integer
        isExcluded:
          type: boolean
          example: false
          default: false
        ankaRate:
          type: number
          format: float
          example: 5
        ankaCost:
          type: number
          format: float
          example: 72030
        currency:
          $ref: "#/components/schemas/Currency"
        modularWs:
          type: string
          example: L1
        partWs:
          type: string
          example: L2
      required:
        - id
    RepairExclusionsRequest:
      description: Representation of a quotation Repair Exclusions request body
      type: object
      properties:
        repairExclusionsInput:
          type: array
          items:
            $ref: "#/components/schemas/RepairExclusionsInput"
      required:
        - repairExclusionsInput
    RepairExclusionsInput:
      description: Repair Exclusions user input data per specific part type
      type: object
      properties:
        id:
          type: integer
          format: int64
        isExcluded:
          type: boolean
          example: true
      required:
        - id
        - isExcluded
    ModularPricingResponse:
      description: The response for Modular Pricing request
      type: object
      properties:
        data:
          $ref: "#/components/schemas/ModularPricing"
        error:
          $ref: "#/components/schemas/Error"
    ModularPricing:
      description: Representation of a quotation Modular Pricing
      type: object
      properties:
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/ModularPricingWorkscope"
        progress:
          $ref: "#/components/schemas/Progress"
    ModularPricingWorkscope:
      description: Representation of a quotation Modular Pricing Workscope
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        isSystem:
          type: boolean
          example: false
        modules:
          type: array
          items:
            $ref: "#/components/schemas/ModularPricingModule"
      required:
        - id
        - name
        - isSystem
        - modules
    ModularPricingModule:
      description: Representation of a quotation Modular Pricing Module
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        name:
          type: string
          nullable: true
        fpNtePrice:
          type: number
          format: double
          example: 9999.99
          nullable: true
        dbii:
          type: number
          format: double
          example: 15.9
          maximum: 100
          nullable: true
        productionCostIncl:
          type: integer
          nullable: true
        productionCostExcl:
          type: integer
          nullable: true
        revenueIncl:
          type: integer
          nullable: true
        revenueExcl:
          type: integer
          nullable: true
        modularWs:
          type: string
          example: WS_1_K
          nullable: true
      required:
        - id
        - name
        - fpNtePrice
        - dbii
        - productionCostIncl
        - productionCostExcl
        - revenueIncl
        - revenueExcl
        - modularWs
    ModularPricingRequest:
      description: Representation of a quotation Modular Pricing request body
      type: object
      properties:
        modularPricingInput:
          type: array
          items:
            $ref: "#/components/schemas/ModularPricingInput"
      required:
        - modularPricingInput
    ModularPricingInput:
      description: Modular Pricing user input data per specific part type
      type: object
      properties:
        id:
          type: integer
          format: int64
        fpNtePrice:
          type: number
          format: double
          example: 9999.99
        dbii:
          type: number
          format: double
          example: 15.9
          maximum: 100
          exclusiveMaximum: true
      required:
        - id
    EscalationPricingInputValues:
      type: object
      properties:
        labourPrices:
          type: number
          format: float
        eparPrices:
          type: number
          format: float
        rfpLabour:
          type: number
          format: float
        hcMaterialPrices:
          type: number
          format: float
        hcSubcontractPrices:
          type: number
          format: float
        fpNtePrices:
          type: number
          format: float
    EscalationPricingInput:
      description: Representation of a quotation pricing escalation input per year
      type: object
      allOf:
        - $ref: "#/components/schemas/EscalationPricingInputValues"
      properties:
        id:
          type: integer
          format: int64
        year:
          type: string
          minimum: 4
          maximum: 4
      required:
        - id
    EscalationPricingRequest:
      description: Representation of a quotation Escalation Pricing input data
      type: object
      properties:
        escalationInputs:
          type: array
          items:
            $ref: "#/components/schemas/EscalationPricingInput"
        materialPrice:
          type: number
          format: float
          nullable: true
        labourPrice:
          type: number
          format: float
          nullable: true
    EscalationFieldValue:
      description: Representation of a quotation pricing escalation field values
      type: object
      properties:
        value:
          type: number
          format: float
        defaultValue:
          type: number
          format: float
    EscalationValues:
      description: Representation of a quotation pricing escalation values
      type: object
      properties:
        labourPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        eparPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        rfpLabour:
          $ref: "#/components/schemas/EscalationFieldValue"
        hcMaterialPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        hcSubcontractPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        fpNtePrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        materialEscalationCost:
          $ref: "#/components/schemas/EscalationFieldValue"
    Escalation:
      description: Representation of a quotation pricing escalation per year
      type: object
      properties:
        id:
          type: integer
          format: int64
        year:
          type: string
          minimum: 4
          maximum: 4
        values:
          $ref: "#/components/schemas/EscalationValues"
      required:
        - id
    EscalationsPricing:
      description: Representation of a quotation Escalations Pricing
      type: object
      properties:
        escalationsPricing:
          type: array
          items:
            $ref: "#/components/schemas/Escalation"
        canCurrentUserEdit:
          type: boolean
        materialPrice:
          type: number
          format: float
        labourPrice:
          type: number
          format: float
        progress:
          $ref: "#/components/schemas/Progress"
    EscalationsPricingResponse:
      description: The response for Escalations Pricing.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/EscalationsPricing"
        error:
          $ref: "#/components/schemas/Error"
    RevenueCapRequest:
      description: Representation of a quotation Revenue Cap request body for specific workscope
      type: object
      properties:
        workscopeId:
          type: integer
          format: int64
        revenueCap:
          type: integer
          format: int64
          nullable: true
    WorkscopeSummaryItem:
      description: Representation of a quotation Workscope Summary Item
      type: object
      properties:
        year:
          type: string
          minimum: 4
          maximum: 4
        revenue:
          type: integer
        revenueCap:
          type: integer
        productionCost:
          type: integer
        discount:
          type: integer
        surchargesCost:
          type: integer
        productionCostAfterDiscountAndSurcharges:
          type: integer
        db2:
          type: integer
        db2Percentage:
          type: number
          format: float
        ebit:
          type: integer
        ebitPercentage:
          type: number
          format: float
        eatPercentage:
          type: number
          format: float
        netMargin:
          type: number
          format: float
    WorkscopeSummary:
      description: Representation of a quotation Workscope Summary
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        realisticWsId:
          type: integer
          format: int64
          nullable: true
        includedItems:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeSummaryItem"
        excludedItems:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeSummaryItem"
        totalItems:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeSummaryItem"
      required:
        - id
        - realisticWsId
    WorkscopeSummaryResponse:
      description: The response for Workscope Summary request.
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeSummary"
        error:
          $ref: "#/components/schemas/Error"
    ExchangeRates:
      description: Representation of Exchange Rates
      type: object
      properties:
        oldRate:
          type: number
          example: 1.0
        newRate:
          type: number
          example: 1.0
    ExchangeRatesResponse:
      description: The response for Update and Get ExchangeRate request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/ExchangeRates"
        error:
          $ref: "#/components/schemas/Error"
    WorkscopeCommentsResponse:
      description: The response for Workscope Comments request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationWorkscopeComment"
        error:
          $ref: "#/components/schemas/Error"
    QuotationWorkscopeComment:
      type: object
      properties:
        quotationId:
          type: integer
          format: int64
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeCommentGroup"
    WorkscopeCommentGroup:
      type: object
      properties:
        workscopeId:
          type: integer
          format: int64
        name:
          type: string
        modules:
          type: array
          items:
            $ref: "#/components/schemas/MajorModuleComment"
    MajorModuleComment:
      allOf:
        - $ref: "#/components/schemas/ModuleComment"
        - type: object
          properties:
            submodules:
              type: array
              items:
                $ref: "#/components/schemas/ModuleComment"
    ModuleComment:
      type: object
      properties:
        name:
          type: string
        comment:
          type: string
    OperationProgressState:
      description: Representation of async operations progress state.
      type: object
      properties:
        operationProgress:
          $ref: "#/components/schemas/ProgressState"
        isLocked:
          type: boolean
          example: true
    ProgressStateResponse:
      description: The response for Get quotation workscope summary calculation state request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/OperationProgressState"
        error:
          $ref: "#/components/schemas/Error"
    AsyncCalculationStateResponse:
      description: Response for triggering an async calculation job.
      type: object
      properties:
        data:
          type: string
          example: Your calculation request has been accepted and is being processed.
        error:
          $ref: "#/components/schemas/Error"
    CommentResponse:
      description: The response for Comments request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/Comment"
        error:
          $ref: "#/components/schemas/Error"
    Comment:
      description: Representation of a quotation Comment
      type: object
      properties:
        quotationId:
          type: integer
          format: int64
        content:
          type: string
    CommentRequest:
      description: Representation of a quotation Comments request body
      type: object
      properties:
        content:
          type: string
          maximum: 150
          nullable: true
    EparPrices:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        engineName:
          type: string
        engineVersion:
          type: string
        currency:
          $ref: "#/components/schemas/Currency"
        year:
          type: string
          minimum: 4
          maximum: 4
        cleaningAndInspection:
          type: string
        repair:
          type: string
      required:
        - engineName
        - currency
        - year
        - cleaningAndInspection
        - repair
    EparPricesRequest:
      description: Representation of an Epar Prices input data
      type: object
      properties:
        eparPrices:
          type: array
          items:
            $ref: "#/components/schemas/EparPrices"
    EparPricesResponse:
      description: The response for Epar Prices
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/EparPrices"
        error:
          $ref: "#/components/schemas/Error"
    ConditionalTask:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        currency:
          $ref: "#/components/schemas/Currency"
        year:
          type: string
          minimum: 4
          maximum: 4
        conditionalTask:
          type: string
      required:
        - currency
        - year
        - conditionalTask
    ConditionalTasksRequest:
      description: Representation of a Conditional Tasks input data
      type: object
      properties:
        conditionalTasks:
          type: array
          items:
            $ref: "#/components/schemas/ConditionalTask"
    ConditionalTasksResponse:
      description: The response for Conditional Tasks request.
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/ConditionalTask"
        error:
          $ref: "#/components/schemas/Error"
    UserData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        sapUsername:
          type: string
          nullable: true
        uNumber:
          type: string
          maxLength: 7
        name:
          type: string
          nullable: true
      required:
        - uNumber
    UserDataRequest:
      description: Representation of a User Data input data
      type: object
      properties:
        usersData:
          type: array
          items:
            $ref: "#/components/schemas/UserData"
    UserDataResponse:
      description: The response for User Data request.
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/UserData"
        error:
          $ref: "#/components/schemas/Error"
    Engine:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
      required:
        - id
        - name
    EngineResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Engine'
        error:
          $ref: '#/components/schemas/Error'
    TaskData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        name:
          type: string
        clusterId:
          type: integer
          format: int64
          nullable: true
      required:
        - name
        - clusterId
    TaskDataRequest:
      description: Representation of a Task Data input data
      type: object
      properties:
        tasksData:
          type: array
          items:
            $ref: "#/components/schemas/TaskData"
    ClusterData:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
      required:
        - id
        - name
    ClustersTasksMatrix:
      type: object
      properties:
        tasks:
          type: array
          items:
            $ref: '#/components/schemas/TaskData'
        clusters:
          type: array
          items:
            $ref: '#/components/schemas/ClusterData'
        engineId:
          type: integer
          format: int64
    ClustersTasksMatrixResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ClustersTasksMatrix'
        error:
          $ref: '#/components/schemas/Error'
    PartData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        name:
          type: string
        clusterId:
          type: integer
          format: int64
          nullable: true
        type:
          $ref: "#/components/schemas/PartType"
      required:
        - name
        - clusterId
        - type
    PartDataRequest:
      description: Representation of a Part Data input data
      type: object
      properties:
        partsData:
          type: array
          items:
            $ref: "#/components/schemas/PartData"
    ClustersPartsMatrix:
      type: object
      properties:
        parts:
          type: array
          items:
            $ref: '#/components/schemas/PartData'
        clusters:
          type: array
          items:
            $ref: '#/components/schemas/ClusterData'
        engineId:
          type: integer
          format: int64
    ClustersPartsMatrixResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ClustersPartsMatrix'
        error:
          $ref: '#/components/schemas/Error'
    AvailablePartsNamesResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: string
        error:
          $ref: '#/components/schemas/Error'
    NonExpendableDiscountData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        nonLlpTieredPercentage:
          type: number
        nonLlpEscalatedPercentage:
          type: number
        llpTieredPercentage:
          type: number
        llpEscalatedPercentage:
          type: number
      required:
        - nonLlpTieredPercentage
        - nonLlpEscalatedPercentage
        - llpTieredPercentage
        - llpEscalatedPercentage
    EngineNonExpendableDiscountData:
      type: object
      properties:
        engineId:
          type: integer
          format: int64
        engineName:
          type: string
        discountsDataByYear:
          type: array
          items:
            $ref: '#/components/schemas/NonExpendableDiscountData'
      required:
        - engineId
        - engineName
        - discountsDataByYear
    MaterialNonExpendableDiscountsConditions:
      type: array
      items:
        $ref: '#/components/schemas/EngineNonExpendableDiscountData'
    MaterialNonExpendableDiscountConditionsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/MaterialNonExpendableDiscountsConditions'
        error:
          $ref: '#/components/schemas/Error'
    MaterialNonExpendableDiscountConditionsRequest:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/MaterialNonExpendableDiscountsConditions'
    ExpendableDiscountData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        levelZeroPercentage:
          type: number
        levelOnePercentage:
          type: number
        levelTwoPercentage:
          type: number
      required:
        - year
        - levelZeroPercentage
        - levelOnePercentage
        - levelTwoPercentage
    EngineExpendableDiscountData:
      type: object
      properties:
        engineId:
          type: integer
          format: int64
        engineName:
          type: string
        expendableDiscountsDataByYear:
          type: array
          items:
            $ref: '#/components/schemas/ExpendableDiscountData'
      required:
        - engineId
        - engineName
        - expendableDiscountsDataByYear
    MaterialExpendableDiscountsConditions:
      type: object
      properties:
        expendableLevelZero:
          type: integer
          format: int32
          nullable: true
        expendableLevelOne:
          type: integer
          format: int32
          nullable: true
        expendableLevelTwo:
          type: integer
          format: int32
          nullable: true
        expendableDiscountDataByEngine:
          type: array
          items:
            $ref: '#/components/schemas/EngineExpendableDiscountData'
    MaterialExpendableDiscountConditionsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/MaterialExpendableDiscountsConditions'
        error:
          $ref: '#/components/schemas/Error'
    MaterialExpendableDiscountConditionsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/EngineExpendableDiscountData'
    SubcontractDiscountConditionsData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        discountPercentage:
          type: number
      required:
        - year
        - discountPercentage
    SubcontractDiscountConditions:
      type: object
      properties:
        subcontractor:
          type: string
        discountConditionsDataByYear:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractDiscountConditionsData'
      required:
        - subcontractor
        - discountConditionsDataByYear
    SubcontractDiscountConditionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractDiscountConditions'
        error:
          $ref: '#/components/schemas/Error'
    SubcontractDiscountConditionsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractDiscountConditions'
    SubcontractDiscountPercentageBySubcontractor:
      type: object
      properties:
        subcontractor:
          type: string
        discountPercentage:
          type: number
      required:
        - subcontractor
        - discountPercentage
    SubcontractDiscountPortionsData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        partName:
          type: string
        discountsPercentageBySubcontractor:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractDiscountPercentageBySubcontractor'
      required:
        - partName
        - discountsPercentageBySubcontractor
    SubcontractDiscountPortionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractDiscountPortionsData'
        error:
          $ref: '#/components/schemas/Error'
    SubcontractDiscountPortionsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractDiscountPortionsData'
    MaterialDiscountPortionsData:
      type: object
      properties:
        id:
          type: integer
          format: int64
        partType:
          $ref: '#/components/schemas/PartType'
        expendablePercentage:
          type: number
        nonExpendablePercentage:
          type: number
        llpPercentage:
          type: number
        isExpendableEditable:
          type: boolean
        isNonExpendableEditable:
          type: boolean
        isLlpEditable:
          type: boolean
      required:
        - id
        - partType
        - expendablePercentage
        - nonExpendablePercentage
        - llpPercentage
        - isExpendableEditable
        - isNonExpendableEditable
        - isLlpEditable
    MaterialDiscountPortions:
      type: object
      properties:
        labourType:
          type: string
        partTypeDiscountsByLabourType:
          type: array
          items:
            $ref: '#/components/schemas/MaterialDiscountPortionsData'
      required:
        - labourType
        - partTypeDiscountsByLabourType
    MaterialDiscountPortionsDataUpdate:
      type: object
      properties:
        id:
          type: integer
          format: int64
        partType:
          $ref: '#/components/schemas/PartType'
        expendablePercentage:
          type: number
        nonExpendablePercentage:
          type: number
        llpPercentage:
          type: number
      required:
        - id
        - partType
        - expendablePercentage
        - nonExpendablePercentage
        - llpPercentage
    MaterialDiscountPortionsUpdate:
      type: object
      properties:
        labourType:
          type: string
        partTypeDiscountsByLabourType:
          type: array
          items:
            $ref: '#/components/schemas/MaterialDiscountPortionsDataUpdate'
      required:
        - labourType
        - partTypeDiscountsByLabourType
    MaterialDiscountPortionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MaterialDiscountPortions'
        error:
          $ref: '#/components/schemas/Error'
    MaterialDiscountPortionsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MaterialDiscountPortionsUpdate'
    MaterialVolumeBasedDiscountData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: false
        spendTargetNonLlpNewMaterialPerQsv:
          type: number
        minimumSpendTargetPerQsv:
          type: number
        maximumVolumeBasedMaterialCreditPerQsv:
          type: number
    MaterialVolumeBasedDiscounts:
      type: object
      properties:
        year:
          type: string
        volumeBasedDiscountsDataByYear:
          type: array
          items:
            $ref: '#/components/schemas/MaterialVolumeBasedDiscountData'
      required:
        - year
        - volumeBasedDiscountsDataByYear
    MaterialVolumeBasedDiscountsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MaterialVolumeBasedDiscounts'
        error:
          $ref: '#/components/schemas/Error'
    MaterialVolumeBasedDiscountsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MaterialVolumeBasedDiscounts'
    CommitmentLetterDiscountsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/EngineNonExpendableDiscountData'
        error:
          $ref: '#/components/schemas/Error'
    CommitmentLetterDiscountsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/EngineNonExpendableDiscountData'
    MaterialEscalationAssumptionsData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        escalationPercentage:
          type: number
      required:
        - year
        - escalationPercentage
    MaterialEscalationAssumptions:
      type: object
      properties:
        engineName:
          type: string
        engineId:
          type: integer
          format: int64
        materialEscalationAssumptionsByYear:
          type: array
          items:
            $ref: '#/components/schemas/MaterialEscalationAssumptionsData'
      required:
        - engineName
        - engineId
        - materialEscalationAssumptionsByYear
    MaterialEscalationAssumptionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MaterialEscalationAssumptions'
        error:
          $ref: '#/components/schemas/Error'
    MaterialEscalationAssumptionsRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MaterialEscalationAssumptions'
    LabourSurchargesByYear:
      type: object
      properties:
        ah_rate_v25_cfm56_cf6_leap:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20,
          message = "The value must be a valid positive number")'
        ah_rate_v25_cfm56_cf6_leap_yoy_percentage:
          type: number
          minimum: -100
          maximum: 100
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 3, fraction = 20,
          message = "Percentage must be between -100 and 100")'
        tec_surcharge_per_ws_a_inh_standard:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        tec_surcharge_per_ws_a_inh_leap_cfm_oem_offload:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        tec_surcharge_per_ws_a_sc_dlh_swr_pw1_ovh:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        tec_surcharge_enl_per_month_per_lease_event:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        tec_surcharge_per_ws_b_fixed_part_inh_sc_oem_offloads:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        tec_surcharge_per_ws_b_c_variable_part_inh_sc_oem_offloads_percentage:
          type: number
          minimum: 0
          maximum: 100
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 3, fraction = 20, 
          message = "Percentage must be between 0 and 100")'
        teo21_22_surcharge_per_ws_a_b:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        teo21_22_surcharge_per_mes_hub_module_event_ham:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
    LabourSurcharges:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        labourSurchargesByYear:
          $ref: '#/components/schemas/LabourSurchargesByYear'
      required:
        - year
        - labourSurchargesByYear
    LabourSurchargesResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LabourSurcharges'
        error:
          $ref: '#/components/schemas/Error'
    LabourSurchargesRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LabourSurcharges'
    InternalLogisticRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/InternalLogisticSurcharges'
    InternalLogisticResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/InternalLogisticSurcharges'
        error:
          $ref: '#/components/schemas/Error'
    InternalLogisticSurcharges:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        internalLogisticSurchargesByYear:
          $ref: '#/components/schemas/InternalLogisticSurchargesByYear'
      required:
        - year
        - internalLogisticSurchargesByYear
    InternalLogisticSurchargesByYear:
      type: object
      properties:
        wsA:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        wsB:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
        wsC:
          type: number
          minimum: 0
          maximum: 999999999
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, 
          message = "The value must be a valid positive number")'
    MiscellaneousSurchargesRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MiscellaneousSurcharges'
    MiscellaneousSurchargesResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/MiscellaneousSurcharges'
        error:
          $ref: '#/components/schemas/Error'
    MiscellaneousSurcharges:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        miscellaneousSurchargesByYear:
          $ref: '#/components/schemas/MiscellaneousSurchargesByYear'
      required:
        - year
        - miscellaneousSurchargesByYear
    MiscellaneousSurchargesByYear:
      type: object
      properties:
        hurdleEnsPercentage:
          type: number
        hurdleMesPercentage:
          type: number
        waccPercentage:
          type: number
      required:
        - hurdleEnsPercentage
        - hurdleMesPercentage
        - waccPercentage
    LossOfValueRequest:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LossOfValueSurcharges'
    LossOfValueResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LossOfValueSurcharges'
        error:
          $ref: '#/components/schemas/Error'
    LossOfValueSurcharges:
      type: object
      properties:
        id:
          type: integer
          format: int64
          nullable: true
        year:
          type: string
          minimum: 4
          maximum: 4
        lossOfValuePercentage:
          type: number
          minimum: 0
          maximum: 100
          example: 15.9
          x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 3, fraction = 20, message = "Percentage must be between 0 and 100")'
      required:
        - year
        - lossOfValuePercentage
    LeapLicenceFeesRequest:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/LeapLicenceFees'
    LeapLicenceFeesResponse:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/LeapLicenceFees'
            error:
              $ref: '#/components/schemas/Error'
    LeapLicenceFees:
          type: object
          properties:
            id:
              type: integer
              format: int64
              nullable: true
            year:
              type: string
              minimum: 4
              maximum: 4
            leapLicenceFeesByYear:
              $ref: '#/components/schemas/LeapLicenceFeesByYear'
          required:
            - year
            - leapLicenceFeesByYear
    LeapLicenceFeesByYear:
          type: object
          properties:
            wsALeap1aCbsaFee:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            wsBLeap1aCbsaFee:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            wsALeap1bCbsaFee:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            wsBLeap1bCbsaFee:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
          required:
            - wsALeap1aCbsaFee
            - wsBLeap1aCbsaFee
            - wsALeap1bCbsaFee
            - wsBLeap1bCbsaFee
    LeapUtilizationFeesRequest:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/LeapUtilizationFees'
    LeapUtilizationFeesResponse:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/LeapUtilizationFees'
            error:
              $ref: '#/components/schemas/Error'
    LeapUtilizationFees:
          type: object
          properties:
            id:
              type: integer
              format: int64
              nullable: true
            year:
              type: string
              minimum: 4
              maximum: 4
            leapUtilizationFeesByYear:
              $ref: '#/components/schemas/LeapUtilizationFeesByYear'
          required:
            - year
            - leapUtilizationFeesByYear
    LeapUtilizationFeesByYear:
          type: object
          properties:
            wsAUtilizationFee:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            wsBUtilizationFee:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
          required:
            - wsAUtilizationFee
            - wsBUtilizationFee
    ThirdPartyCreditRequest:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ThirdPartyCredit'
    ThirdPartyCreditResponse:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ThirdPartyCredit'
            error:
              $ref: '#/components/schemas/Error'
    ThirdPartyCredit:
          type: object
          properties:
            id:
              type: integer
              format: int64
              nullable: true
            year:
              type: string
              minimum: 4
              maximum: 4
            thirdPartyCreditByYear:
              $ref: '#/components/schemas/ThirdPartyCreditByYear'
          required:
            - year
            - thirdPartyCreditByYear
    ThirdPartyCreditByYear:
          type: object
          properties:
            discountPercentage:
              type: number
              minimum: 0
              maximum: 100
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 3, fraction = 20, message = "Percentage must be between 0 and 100")'
            thirdPartyCap:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            thirdPartyModerate:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            thirdPartyHarsh:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
            thirdPartySupplemental:
              type: number
              minimum: 0
              maximum: 999999999
              x-field-extra-annotation: '@jakarta.validation.constraints.Digits(integer = 9, fraction = 20, message = "The value must be a valid positive number")'
          required:
            - discountPercentage
            - thirdPartyCap
            - thirdPartyModerate
            - thirdPartyHarsh
            - thirdPartySupplemental
    ContractType:
      type: string
      enum:
        - T&M
        - RFP
        - WS-FP
        - WS-NTE
        - Mod-FP
        - Mod-NTE
    QuotationSort:
      type: string
      enum:
        - STATUS
        - LAST_UPDATE
        - OFFER_NUMBER
        - CUSTOMER
        - ENGINE_TYPE
        - OWNER
      default: LAST_UPDATE
    QuotationStatus:
      type: string
      enum:
        - ANKA_VALIDATED
        - IN_PROGRESS
        - TRANSFERRED
        - COMPLETED
        - UNARCHIVED
        - ARCHIVED
      description: The completion status of a given quotation
    PartType:
      type: string
      enum:
        - A_PART
        - CASE_AND_FRAME
        - LLP
        - KIT
        - COMPONENT
        - PARTS_PACKAGE
        - ROUTINE_MATERIAL
        - NON_ROUTINE_MATERIAL
      default: A_PART
    Currency:
      type: string
      enum:
        - USD
        - EUR
      default: USD
    NavigationSteps:
      type: string
      enum:
        - COVER
        - MATERIAL_PRICING
        - LABOUR_PRICING
        - SUBCONTRACT_PRICING
        - REPAIR_EXCLUSIONS
        - MODULAR_PRICING
        - PRICING_ESCALATION
        - WORKSCOPE_SUMMARY
    QuotationProgress:
      type: string
      enum:
        - COVER
        - SCRAP_CAPS
        - HANDLING_CHARGES
        - USM_RATINGS
        - PMA_RATINGS
        - COMMITMENT_LETTER
        - LABOUR_RATE_AND_EPAR
        - RLFP_ENGINE
        - RLFP_MODULE
        - SUBCONTRACT_PRICING
        - REPAIR_EXCLUSIONS
        - MODULAR_PRICING
        - PRICING_ESCALATION
        - WORKSCOPE_SUMMARY
    ProgressState:
      type: string
      enum:
        - INITIALIZED
        - IN_PROGRESS
        - COMPLETED
        - FAILED
      description: The completion status of a given quotation
    BeginQuotationType:
      type: string
      enum:
        - UPDATE
        - COPY
        - DEFAULT
    ApiError:
      required:
        - code
        - description
        - status
      type: object
      properties:
        status:
          type: integer
          format: int32
        code:
          type: string
        description:
          type: string
      description: Common API error model.
    ErrorResponse:
      description: This is the response object in case of errors, compliant with RFC7807
      type: object
      properties:
        error:
          $ref: "#/components/schemas/Error"
    Error:
      description: This is the error object
      type: object
      properties:
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
  responses:
    BadRequest:
      description: BAD REQUEST
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Unauthorized:
      description: UNAUTHORIZED
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Forbidden:
      description: FORBIDDEN
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    NotFound:
      description: NOT FOUND
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    InternalServerError:
      description: INTERNAL SERVER ERROR
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
  securitySchemes:
    BEARER_JWT:
      type: http
      scheme: bearer
      bearerFormat: JWT
