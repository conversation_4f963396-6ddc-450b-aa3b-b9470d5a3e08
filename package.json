{"name": "coca-ui-host-app", "version": "2.8.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "format:write": "prettier --parser typescript --config .prettierrc.js --write \"src/**/*.{ts,tsx}\"", "generate:api": "ng-openapi-gen --input coca-backend-openapi/coca-service-api.yaml --output ./src/app/core/openapi/"}, "private": true, "dependencies": {"@angular-architects/module-federation": "19.0.3", "@angular/animations": "19.2.2", "@angular/common": "19.2.2", "@angular/compiler": "19.2.2", "@angular/core": "19.2.2", "@angular/forms": "19.2.2", "@angular/material": "19.2.3", "@angular/platform-browser": "19.2.2", "@angular/platform-browser-dynamic": "19.2.2", "@angular/router": "19.2.2", "@lufthansatechnik/bootstrap5": "3.0.1", "@ngrx/effects": "19.0.1", "@ngrx/operators": "19.0.1", "@ngrx/store": "19.0.1", "@ngrx/store-devtools": "19.0.1", "@ngx-translate/core": "16.0.4", "@ngx-translate/http-loader": "16.0.1", "@primeng/themes": "19.0.10", "angular-roles-based-authorisation": "1.0.0", "bootstrap": "5.3.6", "cp-lht-spinner": "0.0.21", "ng-openapi-gen": "0.53.0", "primeicons": "7.0.0", "primeng": "19.0.10", "rxjs": "7.8.2", "tslib": "2.8.1", "virava": "1.5.0", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.3", "@angular/cli": "19.2.3", "@angular/compiler-cli": "19.2.2", "@types/jasmine": "5.1.7", "@typescript-eslint/eslint-plugin": "8.26.1", "@typescript-eslint/parser": "8.26.1", "eslint": "9.22.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.3", "jasmine-core": "5.6.0", "karma": "6.4.4", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "ngx-build-plus": "19.0.0", "prettier": "3.5.3", "typescript": "5.8.2"}}