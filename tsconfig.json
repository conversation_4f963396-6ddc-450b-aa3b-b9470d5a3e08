{"compileOnSave": false, "compilerOptions": {"baseUrl": "./src", "outDir": "./dist/out-tsc", "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "esModuleInterop": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "module": "ES2022", "useDefineForClassFields": false, "lib": ["ES2022", "dom"], "paths": {"@app/*": ["app/*"], "@core/*": ["app/core/*"], "@openapi/*": ["app/core/openapi/*"], "@shared/*": ["app/shared/*"], "@assets/*": ["app/assets/*"]}}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}