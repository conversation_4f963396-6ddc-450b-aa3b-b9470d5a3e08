import { definePreset } from '@primeng/themes';
import Material from '@primeng/themes/material';

const PrimeNgPreset = definePreset(Material, {
  semantic: {
    primary: {
      500: 'var(--lht-blue-100)',
      600: 'var(--lht-blue-100)',
      700: 'var(--lht-blue-100)',
    },
  },

  components: {
    multiselect: {
      colorScheme: {
        light: {
          root: {
            fontSize: '1.4rem',
            fontWeight: 'bold',
            color: 'var(--lht-blue-secondary-radiant-100)',
          },
        },
      },
    },
    treetable: {
      colorScheme: {
        light: {
          header: {
            background: 'var(--lht-grey-1)',
          },
          headerCell: {
            color: 'var(--lht-blue-100)',
          },
          row: {
            color: 'var(--lht-blue-100)',
          },
        },
      },
    },
    checkbox: {
      colorScheme: {
        light: {
          root: {
            borderColor: 'var(--lht-blue-100)',
            hoverBorderColor: 'var(--lht-blue-100)',
            focusBorderColor: 'var(--lht-blue-100)',
            checkedBackground: 'var(--lht-blue-100)',
            checkedBorderColor: 'var(--lht-blue-100)',
            checkedHoverBackground: 'var(--lht-blue-100)',
            checkedHoverBorderColor: 'var(--lht-blue-100)',
            checkedFocusBorderColor: 'var(--lht-blue-100)',
          },
          icon: {
            checkedColor: 'var(--lht-white)',
            size: '14px',
          },
          label: {
            gap: '0.5rem',
          },
        },
      },
    },
    autocomplete: {
      colorScheme: {
        light: {
          input: {
            background: 'var(--lht-white)',
            color: 'var(--color-text-dark)',
          },
          dropdown: {
            background: 'var(--lht-blue-100)',
            hoverBackground: 'var(--lht-blue-100)',
            activeBackground: 'var(--lht-blue-100)',
            hoverColor: 'var(--lht-white)',
            activeColor: 'var(--lht-white)',
          },
          clearIcon: {
            color: 'var(--color-text-dark)',
          },
          option: {
            selectedBackground: 'var(--lht-container-emphasis)',
            hoverBackground: 'var(--lht-blue-100)',
          },
        },
      },
      option: {
        selectedFocusColor: 'var(--lht-blue-100)',
      },
    },
    toast: {
      summary: {
        fontWeight: 'bold',
        fontSize: '1.5rem',
      },
      detail: {
        fontSize: '1.5rem',
      },
      colorScheme: {
        light: {
          success: {
            detailColor: '{green.600}',
          },
          info: {
            detailColor: '{blue.600}',
          },
          warn: {
            detailColor: '{yellow.900}',
          },
          error: {
            detailColor: '{red.600}',
          },
        },
      },
    },
  },
});

export default PrimeNgPreset;
