@use '@angular/material' as mat;

@use 'variables';

@include mat.core();

$light-primary-text: variables.$lht-white;

$lht-radiant-petrol-blue: (
  50: $lht-blue-secondary-radiant-5,
  100: $lht-blue-secondary-radiant-20,
  200: $lht-blue-secondary-radiant-40,
  300: $lht-blue-secondary-radiant-60,
  400: $lht-blue-secondary-radiant-80,
  500: $lht-blue-secondary-radiant-100,
  600: $lht-blue-secondary-radiant-100,
  700: $lht-blue-secondary-radiant-100,
  800: $lht-blue-secondary-radiant-100,
  900: $lht-blue-secondary-radiant-100,
  A100: $lht-blue-secondary-radiant-20,
  A200: $lht-blue-secondary-radiant-40,
  A400: $lht-blue-secondary-radiant-60,
  A700: $lht-blue-secondary-radiant-100,
  contrast: (
    500: $light-primary-text,
  ),
);

$lht-primary-blue: (
  50: $lht-blue-5,
  100: $lht-blue-20,
  200: $lht-blue-40,
  300: $lht-blue-60,
  400: $lht-blue-80,
  500: $lht-blue-100,
  600: $lht-blue-100,
  700: $lht-blue-100,
  800: $lht-blue-100,
  900: $lht-blue-100,
  A100: $lht-blue-20,
  A200: $lht-blue-40,
  A400: $lht-blue-60,
  A700: $lht-blue-100,
  contrast: (
    500: $light-primary-text,
  ),
);

@mixin custom-button-toggle-group($theme) {
  .mat-button-toggle-group {
    box-shadow: 1px 1px 5px 1px variables.$lht-grey-2 !important;
  }

  .mat-button-toggle {
    background-color: variables.$lht-white !important;
    border: 1px solid variables.$lht-grey-1 !important;
    color: variables.$lht-blue-100 !important;
  }
  .mat-button-toggle:nth-child(2) {
    border-left: none !important;
  }

  .mat-button-toggle-checked {
    background-color: variables.$lht-white !important;
    border-bottom: 3px solid variables.$lht-blue-secondary-radiant-100 !important;
  }
}

@mixin custom-form-field-theme($theme) {
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: variables.$lht-blue-100;
  }
}

@mixin custom-select-option-theme($theme) {
  .mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,
  .mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full {
    background-color: variables.$lht-blue-80;
  }
}

@mixin custom-checkbox-theme($theme) {
  .mat-mdc-checkbox.mat-primary {
    // it doesn't allow us to use variables here. Colors must be set like this
    --mdc-checkbox-selected-focus-icon-color: #374571;
    --mdc-checkbox-selected-hover-icon-color: #374571;
    --mdc-checkbox-selected-icon-color: #374571;
    --mdc-checkbox-selected-pressed-icon-color: #374571;
  }
}

// buttons
$my-buttons-theme: mat.m2-define-light-theme(
  (
    color: (
      primary: mat.m2-define-palette($lht-radiant-petrol-blue, 500),
      accent: mat.m2-define-palette($lht-radiant-petrol-blue, 300),
    ),
  )
);
@include mat.button-theme($my-buttons-theme);

.mat-mdc-raised-button.mat-primary,
.mat-mdc-unelevated-button.mat-primary {
  color: $light-primary-text !important;
}

// form-fields
$my-form-field-theme: mat.m2-define-light-theme(
  (
    color: (
      primary: mat.m2-define-palette($lht-primary-blue, 400),
      accent: mat.m2-define-palette($lht-primary-blue, 300),
    ),
  )
);

// Fonts for the whole app
$my-custom-typography: mat.m2-define-typography-config(
  $font-family: 'Main-font, sans-serif',
);

@include mat.form-field-theme($my-form-field-theme);
@include custom-form-field-theme($my-form-field-theme);
@include custom-select-option-theme($my-form-field-theme);
@include custom-checkbox-theme($my-form-field-theme);
@include mat.all-component-typographies($my-custom-typography);
@include custom-button-toggle-group($my-form-field-theme);

.browse-wrapper {
  display: flex;
  align-items: center;
}
