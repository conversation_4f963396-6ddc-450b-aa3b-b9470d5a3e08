@use 'variables';

// Buttons colors
.stepper-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.custom-btn-primary {
  width: fit-content;
}

.custom-btn-primary:not(.mat-button-disabled) {
  color: variables.$lht-white;
  background-color: variables.$lht-blue-semantic-100-info;

  &:hover {
    background-color: variables.$color-text-dark;
  }
}

.custom-btn-secondary {
  width: fit-content;
  color: variables.$lht-blue-semantic-100-info;
  border-color: variables.$lht-blue-semantic-100-info;
}

.bg-danger {
  background-color: variables.$lht-red-100-danger !important;

  &:hover {
    background-color: variables.$lht-red-70-danger-hover !important;
  }
}

// Checkboxes
::ng-deep .mat-checkbox-label {
  font-weight: 600;
}

::ng-deep .mat-checkbox-checked.mat-accent:not(.mat-checkbox-disabled) .mat-checkbox-background,
::ng-deep .mat-checkbox-ripple .mat-ripple-element {
  background-color: variables.$lht-blue-semantic-100-info !important;
}

// Radio buttons color
::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle,
::ng-deep .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  background-color: variables.$lht-blue-semantic-100-info !important;
}

::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: variables.$lht-blue-semantic-100-info !important;
}

// Input
::ng-deep .mat-form-field-required-marker {
  color: variables.$lht-red-100-danger;
}

.fix-height {
  ::ng-deep .mat-form-field-flex > .mat-form-field-infix {
    padding: 0.8rem 0px !important;
  }

  ::ng-deep .mat-form-field-label-wrapper {
    top: -1.2rem;
  }

  ::ng-deep
    .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label {
    width: 133.33333%;
    transform: translateY(-1.4rem) scale(0.55);
  }
}

// Input fields autofill background color
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

// Dialog
::ng-deep .mat-dialog-container {
  width: 300px !important;
  line-height: 1.5;
  text-align: center;

  .mat-dialog-content {
    margin-bottom: 1rem;
    overflow: hidden;
  }
}

// Datepicker
::ng-deep .mat-calendar-body-cell-content.mat-focus-indicator.mat-calendar-body-selected {
  background-color: variables.$lht-blue-semantic-100-info;
}

::ng-deep .mat-datepicker-toggle-active {
  color: variables.$lht-blue-semantic-100-info;
}

// Toggle button
.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
  background-color: variables.$color-toggle-light;
}

.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
  background-color: variables.$lht-blue-semantic-100-info;
}
