@use 'variables';

@font-face {
  font-family: Main-Font;
  src: url('../fonts/LufthansaText-Light.otf') format('opentype');
  font-weight: 300;
}

@font-face {
  font-family: Main-font;
  src: url('../fonts/LufthansaText-Regular.otf') format('opentype');
}

@font-face {
  font-family: Main-font;
  src: url('../fonts/LufthansaText-RegularItalic.otf') format('opentype');
  font-style: italic;
}

@font-face {
  font-family: Main-font;
  src: url('../fonts/LufthansaText-Bold.otf') format('opentype');
  font-weight: 700;
}

@font-face {
  font-family: Main-font;
  src: url('../fonts/LufthansaText-BoldItalic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: Main-font;
  src: url('../fonts/LufthansaHead-Bold.otf') format('opentype');
  font-weight: 800;
}

@font-face {
  font-family: Main-font;
  src: url('../fonts/LufthansaLogo-Bold.otf') format('opentype');
  font-weight: 900;
}

html {
  height: 100%;
}

body {
  height: 100%;
  font-family: Main-font, sans-serif !important;
  font-weight: 400;
  color: variables.$lht-blue-100;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 800;
}

@media (-webkit-min-device-pixel-ratio: 1.5) {
  body {
    font-size: 1rem;
  }
}
