// Colors
$color-text-disabled: #ffffff80;
$color-text-dark: #1a2e6f;
$color-text-disabled-sidenav: #00000033;
$color-text-dark-gray: #333333;
$color-text-dark-blue: #05164d;
$color-toggle-light: #71bbec;

$breadcrumb-active-link-color: #374571;

// Widths; heights
$width-sidebar: 200px;
$width-sidebar-collapsed: 56px;
$main-container-height: 72px;

// Colors from LHT Design Guide
$lht-white: #ffffff;
$lht-container-emphasis: #f4f4f4;

// primary blue
$lht-blue-100: #05164d;
$lht-blue-80: #374571;
$lht-blue-60: #697394;
$lht-blue-40: #9ba2b8;
$lht-blue-20: #cdd0db;
$lht-blue-5: #f3f3f6;

// radiant petrol - secondary blue
$lht-blue-secondary-radiant-100: #00afcb;
$lht-blue-secondary-radiant-80: #33bfd5;
$lht-blue-secondary-radiant-60: #66cfe0;
$lht-blue-secondary-radiant-40: #99dfea;
$lht-blue-secondary-radiant-20: #cceff5;
$lht-blue-secondary-radiant-5: #f2fbfd;

$lht-green-100-success: #49a66a;
$lht-green-10-success: #edf6f0;

$lht-red-100-danger: #cc0000;
$lht-red-70-danger-hover: #db4c4c;

$lht-blue-semantic-100-info: #0f8dc5;
$lht-blue-semantinc-10-info: #e7f4f9;

$lht-yellow: #ffad00;

$lht-grey-2: #cccccc;
$lht-grey-1: #e0e0e0;

:root {
  --color-text-disabled: #{$color-text-disabled};
  --color-text-dark: #{$color-text-dark};
  --color-text-disabled-sidenav: #{$color-text-disabled-sidenav};
  --color-toggle-light: #{$color-toggle-light};

  --lht-white: #{$lht-white};

  --lht-blue-100: #{$lht-blue-100};
  --lht-blue-80: #{$lht-blue-80};
  --lht-blue-60: #{$lht-blue-60};
  --lht-blue-40: #{$lht-blue-40};
  --lht-blue-20: #{$lht-blue-20};
  --lht-blue-5: #{$lht-blue-5};

  --lht-blue-secondary-radiant-100: #{$lht-blue-secondary-radiant-100};
  --lht-blue-secondary-radiant-80: #{$lht-blue-secondary-radiant-80};
  --lht-blue-secondary-radiant-60: #{$lht-blue-secondary-radiant-60};
  --lht-blue-secondary-radiant-40: #{$lht-blue-secondary-radiant-40};
  --lht-blue-secondary-radiant-20: #{$lht-blue-secondary-radiant-20};
  --lht-blue-secondary-radiant-5: #{$lht-blue-secondary-radiant-5};

  --lht-green-100-success: #{$lht-green-100-success};
  --lht-green-10-success: #{$lht-green-10-success};

  --lht-red-100-danger: #{$lht-red-100-danger};
  --lht-red-70-danger-hover: #{$lht-red-70-danger-hover};

  --lht-blue-semantic-100-info: #{$lht-blue-semantic-100-info};
  --lht-blue-semantic-10-info: #{$lht-blue-semantinc-10-info};

  --lht-yellow: #{$lht-yellow};

  --lht-grey-2: #{$lht-grey-2};
  --lht-grey-1: #{$lht-grey-1};

  --lht-container-emphasis: #{$lht-container-emphasis};
}

// Font weights
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;