{"calculationWorkscopeSummaryError": {"heading": "Calculation error", "returnHomePageText": "Return to home page", "text": "Something went wrong while calculating your Workscope summary. Please contact administrator."}, "common": {"adminPanelIconTooltip": "Admin Panel", "cancel": "Cancel", "configuration": "Configuration", "contactAdministrator": "Contact an administrator to use The Core Calculation application.", "costModule": "Cost Mo<PERSON>", "discardChangesAndLogout": "Discard changes & Log out", "enginesAbbreviation": "ENG", "error": "Error", "goTo": "Go to", "home": "Home", "loaderText": "Collecting the data for your calculations... 🛫", "loaderTextExtended": "Please stay on this page, your data is being processed.", "loaderTextExtended2": "This may take a moment. Thank you for your patience.", "loaderTextExtended3": "We are still working on retrieving the latest data for your calculations.", "logout": "Log Out", "logoutMessage": "You have unsaved changes. Click the Cancel button to go back and save your changes or discard them and log out directly!", "pricingTool": "Pricing Tool", "projects": "Projects", "title": "The Core Calculation"}, "logout": {"logoutConfirmationMessage": "Are you sure you want to log out?"}, "maintenancePage": {"header": "We'll be back soon!", "reloadButton": "Reload page", "subheader": "Our application is currently undergoing scheduled maintenance. Thank you for your patience."}, "pageNotFound": {"heading": "Page not found", "returnHomePageText": "Return to home page", "text": "The page you are looking for might have been removed", "text404": "404"}, "pageServerError": {"heading": "Server error", "returnHomePageText": "Return to home page", "text": "Something went wrong. Please try again later.", "text500": "500"}, "sessionExpiration": {"continue": "Continue", "sessionExpiringHeading": "Session Expiring", "sessionExpiringText": "Your session is about to expire. Be aware that all unsaved changes will be lost after session expiry. Do you want to continue?"}, "sidebar": {"collapse": "Collapse", "help": "Help"}, "toastMessages": {"archive": "Archiving", "completed": "Completed!", "error": "Error", "failed": "Failed", "inProgress": "In progress ...", "rlfpDbiiErrorMessage": "Yield should be less than 100%", "savedChangesMessage": "Your changes have been saved!", "success": "Success", "unarchive": "Unarchiving", "userDeletedSuccessMessage": "User deleted successfully!", "userDeletionFailedMessage": "The user was not deleted successfully"}}