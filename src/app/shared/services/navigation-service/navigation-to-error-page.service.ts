import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { MAIN_ROUTES } from '@core/constants/routes';
import { ErrorPageItems } from '@core/models/error-page.models';

export enum ErrorCodes {
  PAGE_NOT_FOUND = '404',
  SERVER_ERROR = '500',
  CALCULATION_WORKSCOPE_SUMMARY = 'calculationWorkscopeSummary',
  UPDATE_FEATURE_COPY_STATE = 'updateFeatureCopyState',
}

@Injectable({
  providedIn: 'root',
})
export class NavigationToErrorPageService {
  public errorData: { [key in ErrorCodes]: ErrorPageItems } = {
    [ErrorCodes.PAGE_NOT_FOUND]: {
      errorCode: 'pageNotFound.text404',
      errorHeading: 'pageNotFound.heading',
      errorMessage: 'pageNotFound.text',
      returnHomeButtonText: 'pageNotFound.returnHomePageText',
    },
    [ErrorCodes.SERVER_ERROR]: {
      errorCode: 'pageServerError.text500',
      errorHeading: 'pageServerError.heading',
      errorMessage: 'pageServerError.text',
      returnHomeButtonText: 'pageServerError.returnHomePageText',
    },
    [ErrorCodes.CALCULATION_WORKSCOPE_SUMMARY]: {
      errorCode: '',
      errorHeading: 'calculationWorkscopeSummaryError.heading',
      errorMessage: 'calculationWorkscopeSummaryError.text',
      returnHomeButtonText: 'calculationWorkscopeSummaryError.returnHomePageText',
    },
    [ErrorCodes.UPDATE_FEATURE_COPY_STATE]: {
      errorCode: '',
      errorHeading: 'updateFeatureCopyStateError.heading',
      errorMessage: 'updateFeatureCopyStateError.text',
      returnHomeButtonText: 'updateFeatureCopyStateError.returnHomePageText',
    },
  };

  constructor() {}
  public navigateToErrorPage(errorCode: ErrorCodes, router: Router): void {
    router.navigateByUrl(`${MAIN_ROUTES.errorPage}`, { state: this.errorData[errorCode] });
  }
}
