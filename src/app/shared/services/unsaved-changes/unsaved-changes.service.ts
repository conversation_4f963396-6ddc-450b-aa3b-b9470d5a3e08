// Exposed for consumption from MFEs
import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Observable } from 'rxjs';

import { setHasUnsavedChanges } from '@app/store/shared.actions';
import { selectHasUnsavedChanges } from '@app/store/shared.selectors';

@Injectable({
  providedIn: 'root',
})
export class UnsavedChangesService {
  private _hasUnsavedChanges: BehaviorSubject<boolean> = new BehaviorSubject(false);

  constructor(private store: Store) {
    this.store.select(selectHasUnsavedChanges).subscribe(hasChanges => {
      this._hasUnsavedChanges.next(hasChanges);
    });
  }

  getHasUnsavedChanges$(): Observable<boolean> {
    return this._hasUnsavedChanges.asObservable();
  }

  enableUnsavedChanges() {
    this.store.dispatch(setHasUnsavedChanges({ hasUnsavedChanges: true }));
  }

  disableUnsavedChanges() {
    this.store.dispatch(setHasUnsavedChanges({ hasUnsavedChanges: false }));
  }
}
