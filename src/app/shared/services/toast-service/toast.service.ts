import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';

import { ToastTypes } from '@core/models/toast.models';

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  constructor(
    private translationService: TranslateService,
    private messageService: MessageService,
  ) {}

  private showMessage(severity: string, title: string, content: string): void {
    this.messageService.add({
      severity,
      summary: this.translationService.instant(title),
      detail: this.translationService.instant(content),
    });
  }

  showSuccess(title: string, content: string): void {
    this.showMessage(ToastTypes.SUCCESS, title, content);
  }

  showInfo(title: string, content: string): void {
    this.showMessage(ToastTypes.INFO, title, content);
  }

  showWarn(title: string, content: string): void {
    this.showMessage(ToastTypes.WARN, title, content);
  }

  showDanger(content: string, title?: string): void {
    const titleData = title || 'common.error';
    this.showMessage(ToastTypes.DANGER, titleData, content);
  }

  hide(): void {
    this.messageService.clear();
  }
}
