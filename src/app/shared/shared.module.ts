import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { MultiSelectModule } from 'primeng/multiselect';
import { ToastModule } from 'primeng/toast';
import { TreeTableModule } from 'primeng/treetable';

import { MaterialModule } from '@shared/material.module';

import { NavigationToErrorPageService } from './services/navigation-service/navigation-to-error-page.service';
import { UnsavedChangesService } from './services/unsaved-changes/unsaved-changes.service';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    TranslateModule,
    MaterialModule,
    RouterModule,
    ToastModule,
    TreeTableModule,
    MultiSelectModule,
  ],
  exports: [MaterialModule, TreeTableModule, MultiSelectModule, ToastModule],
  providers: [MessageService, UnsavedChangesService, NavigationToErrorPageService],
})
export class SharedModule {}
