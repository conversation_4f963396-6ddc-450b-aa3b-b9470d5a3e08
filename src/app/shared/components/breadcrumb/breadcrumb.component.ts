import { CommonModule } from '@angular/common';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRouteSnapshot, NavigationEnd, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, filter, Observable } from 'rxjs';

import { Breadcrumb, BreadcrumbData } from '@app/core/models/breadcrumb';

@Component({
  selector: 'breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss'],
  standalone: true,
  imports: [CommonModule, TranslateModule],
})
export class BreadcrumbComponent implements OnInit {
  private readonly router = inject(Router);
  private readonly destroyRef = inject(DestroyRef);

  private readonly _breadcrumbs$ = new BehaviorSubject<Breadcrumb[]>([]);
  breadcrumbs$: Observable<Breadcrumb[]> = this._breadcrumbs$.asObservable();

  private initialized = false;

  ngOnInit() {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.updateBreadcrumbs();
      });

    this.updateBreadcrumbs();
  }

  onBreadcrumbClick(event: MouseEvent, link?: string) {
    event.preventDefault();
    if (link) this.router.navigateByUrl(link);
  }

  private updateBreadcrumbs() {
    const breadcrumbs: Breadcrumb[] = [];
    const root = this.router.routerState.snapshot.root;
    this.buildBreadcrumbs(root, breadcrumbs);
    this._breadcrumbs$.next(breadcrumbs);
  }

  private buildBreadcrumbs(route: ActivatedRouteSnapshot | null, breadcrumbs: Breadcrumb[]) {
    if (!route) {
      return;
    }

    // Only process breadcrumb data that is directly defined on this route (not inherited)
    if (route.routeConfig?.data?.['breadcrumb'] && route.data?.['breadcrumb']) {
      const breadcrumbData: BreadcrumbData = route.data['breadcrumb'];
      const paramValues = this.buildParamValues(breadcrumbData, route);

      let title = breadcrumbData.title;
      if (breadcrumbData.paramKey && title === breadcrumbData.paramKey && route.params[breadcrumbData.paramKey]) {
        title = route.params[breadcrumbData.paramKey];
      }

      const breadcrumb: Breadcrumb = {
        title: title,
        link: this.buildLink(route),
        paramKey: breadcrumbData.paramKey,
        paramValues: paramValues,
      };

      // Check if a breadcrumb with the same title already exists to prevent duplicates
      const existingBreadcrumb = breadcrumbs.find(b => b.title === breadcrumb.title);
      if (!existingBreadcrumb) {
        breadcrumbs.push(breadcrumb);
      }
    }

    this.buildBreadcrumbs(route.firstChild, breadcrumbs);
  }

  private buildLink(route: ActivatedRouteSnapshot): string {
    const segments: string[] = [];
    let currentRoute: ActivatedRouteSnapshot | null = route;

    while (currentRoute) {
      if (currentRoute.url.length > 0) {
        segments.unshift(...currentRoute.url.map(segment => segment.path));
      }
      currentRoute = currentRoute.parent;
    }

    return '/' + segments.join('/');
  }

  private buildParamValues(breadcrumbData: BreadcrumbData, route: ActivatedRouteSnapshot): Record<string, string> {
    const paramValues: Record<string, string> = {};

    if (breadcrumbData.paramKey && route.params[breadcrumbData.paramKey]) {
      paramValues[breadcrumbData.paramKey] = route.params[breadcrumbData.paramKey];
    }

    return paramValues;
  }
}
