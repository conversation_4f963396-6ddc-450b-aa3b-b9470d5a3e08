@use 'variables' as *;

.breadcrumb {
  margin-bottom: 0;
  padding: 4px 0px;
  font-size: 14px;
  font-weight: 700;
  line-height: 16px;

  &-item {
    gap: 0;
    padding: 0;

    &::before {
      padding: 0 8px;
    }

    &.active {
      color: $color-text-dark-gray;
    }

    a {
      color: $breadcrumb-active-link-color;
      text-decoration: underline;
      text-underline-offset: 2px;
      cursor: pointer;

      &:hover {
        color: $color-text-dark-blue;
      }
    }
  }
}
