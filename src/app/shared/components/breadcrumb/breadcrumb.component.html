<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    @for (item of breadcrumbs$ | async; track item.title; let itemIsLast = $last) {
      <li
        class="breadcrumb-item icon-link"
        [class.active]="itemIsLast"
        [attr.aria-current]="itemIsLast ? 'page' : null"
      >
        @if (!itemIsLast) {
          <a (click)="onBreadcrumbClick($event, item.link)">
            {{ item.title | translate: item.paramValues }}
          </a>
        } @else {
          {{ item.title | translate: item.paramValues }}
        }
      </li>
    }
  </ol>
</nav>
