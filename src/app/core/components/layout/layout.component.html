<p-toast position="bottom-left" [life]="2000"></p-toast>

<layout-header />

@let isMainSidenavOpen = isMainSidenavOpen$ | async;

<ng-container *ngTemplateOutlet="shouldRemoveSidebar ? withoutSidebar : withSidebar"></ng-container>
<ng-template #withSidebar>
  <div class="layout-container">
    <div [ngClass]="isMainSidenavOpen ? 'sidebar' : 'sidebar-collapsed'">
      <app-sidebar
        [isSideNavShown]="isMainSidenavOpen"
        [routes]="ROUTES"
        [navItems]="NAVITEMS"
        (toggleSidebar)="getSideNavData($event)"
      ></app-sidebar>
    </div>

    <div class="main-content" [ngClass]="isMainSidenavOpen ? 'main-content' : 'main-content-collapsed'">
      <router-outlet></router-outlet>
    </div>
  </div>
</ng-template>

<ng-template #withoutSidebar>
  <router-outlet></router-outlet>
</ng-template>
