@use 'variables';

.layout-container {
  display: flex;
  height: calc(100vh - #{variables.$main-container-height});
}

.sidebar-collapsed {
  min-width: variables.$width-sidebar-collapsed;
}

.main-content {
  width: 100%;
  margin-left: 0 !important;
  padding: 20px;
  overflow-y: scroll;
}

.toast-container {
  max-height: 150px;
}

::ng-deep.p-toast-message {
  .p-toast-message-icon {
    margin-right: 1rem;

    .p-icon {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}
