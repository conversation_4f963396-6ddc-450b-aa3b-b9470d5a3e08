import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';

import { userRole } from '@app/store/shared.selectors';

import { NavItemConfig } from '@core/models/nav-item-config';
import { USER_ROLES } from '@core/models/user-roles.models';

import { PATH_DOUBLE_CHEVRON_LEFT_ICON, PATH_QUESTION_MARK_ICON } from '@shared/constants/svg-paths.constants';

@Component({
  standalone: false,
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent {
  @Input() isSideNavShown: boolean | null = true;
  @Input() routes!: any;
  @Input() navItems!: NavItemConfig[];

  @Output() toggleSidebar = new EventEmitter<boolean>();

  userRole$: Observable<string | undefined> = this.store.select(userRole);

  PATH_QUESTION_MARK_ICON = PATH_QUESTION_MARK_ICON;
  PATH_DOUBLE_CHEVRON_LEFT_ICON = PATH_DOUBLE_CHEVRON_LEFT_ICON;
  USER_ROLES = USER_ROLES;

  constructor(private store: Store) {}

  toggleSideNav(): void {
    this.isSideNavShown = !this.isSideNavShown;
    this.toggleSidebar.emit(this.isSideNavShown);
  }
}
