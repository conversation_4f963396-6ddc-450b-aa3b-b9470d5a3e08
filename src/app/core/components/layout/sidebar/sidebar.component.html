<div class="collapse collapse-horizontal" id="sidebarNavigation">
  <ul class="nav flex-column" aria-label="Navigation links">
    @for (item of navItems; track item.route) {
      @if (!(item.route === routes.adminPanel) || (userRole$ | async) === USER_ROLES.ADMIN) {
        <li>
          <a
            [routerLink]="[item.route]"
            routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: false }"
            class="btn icon-text"
          >
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
              <path [attr.d]="item.iconPath" />
            </svg>
            <span class="label">{{ item.title | translate }}</span>
          </a>
        </li>
      }
    }
  </ul>

  <ul class="nav nav-bottom flex-column" aria-label="Sidebar actions">
    <li class="btn icon-text" type="button">
      <svg class="icon" viewBox="0 0 16 16" fill="currentColor">
        <path [attr.d]="PATH_QUESTION_MARK_ICON"></path>
      </svg>
      <span class="label">{{ 'sidebar.help' | translate }}</span>
    </li>

    <li>
      <a
        type="button"
        class="btn btn-toggle icon-text"
        data-bs-toggle="collapse"
        data-bs-target="#sidebarNavigation"
        aria-expanded="false"
        aria-controls="sidebarCollapseToggle"
        (click)="toggleSideNav()"
      >
        <svg class="icon" viewBox="0 0 16 16" fill="currentColor">
          <path [attr.d]="PATH_DOUBLE_CHEVRON_LEFT_ICON"></path>
        </svg>
        <span class="label">{{ 'sidebar.collapse' | translate }}</span>
      </a>
    </li>
  </ul>
</div>
