@use 'variables';

.collapse-horizontal {
  height: 100%;

  &.collapsing {
    min-width: variables.$width-sidebar-collapsed;
    transition: none;
  }

  &.show {
    width: variables.$width-sidebar;

    .btn {
      width: variables.$width-sidebar;
    }

    .nav.flex-column:not(.nav-bottom) {
      .btn.icon-text:not(.active) {
        font-weight: variables.$font-weight-light;
      }
    }
  }
}