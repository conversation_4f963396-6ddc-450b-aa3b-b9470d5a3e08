import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter } from 'rxjs';

import { setMainSidebarState } from '@app/store/shared.actions';
import { isMainSidebarShown } from '@app/store/shared.selectors';

import { navItems } from '@core/constants/nav-items';
import { MAIN_ROUTES } from '@core/constants/routes';

@Component({
  standalone: false,
  selector: 'layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
})
export class LayoutComponent implements OnInit {
  isMainSidenavOpen$ = this.store.select(isMainSidebarShown);

  // Flag to control the visibility of the sidebar
  shouldRemoveSidebar: boolean = false;
  ROUTES = MAIN_ROUTES;
  NAVITEMS = navItems;

  destroyRef = inject(DestroyRef);

  constructor(
    private readonly store: Store,
    private readonly router: Router,
  ) {}

  ngOnInit(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((event: NavigationEnd) => {
        const currentRoute = this.router.routerState.snapshot.root;
        let noSidebarLayout = this.shouldHideSidebarForRoute(currentRoute);

        this.shouldRemoveSidebar = noSidebarLayout;

        if (this.shouldRemoveSidebar) {
          this.store.dispatch(setMainSidebarState({ isMainSidebarShown: false }));
        }
      });
  }

  private shouldHideSidebarForRoute(route: any): boolean {
    if (route.data?.noSidebarLayout) {
      return true;
    }

    if (route.firstChild) {
      return this.shouldHideSidebarForRoute(route.firstChild);
    }

    return false;
  }

  getSideNavData(isSidenavShown: boolean): void {
    this.store.dispatch(setMainSidebarState({ isMainSidebarShown: isSidenavShown }));
  }
}
