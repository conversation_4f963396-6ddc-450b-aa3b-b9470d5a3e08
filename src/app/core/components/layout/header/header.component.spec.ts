import { TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbsComponent } from './breadcrumbs/breadcrumbs.component';
import { HeaderComponent } from './header.component';

describe('HeaderComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HeaderComponent, BreadcrumbsComponent],
      imports: [TranslateModule.forRoot()],
    }).compileComponents();
  });

  it('Should create the header', () => {
    const fixture = TestBed.createComponent(HeaderComponent);
    const header = fixture.componentInstance;
    expect(header).toBeTruthy();
  });

  it('The header should be 41 px', () => {
    const fixture = TestBed.createComponent(HeaderComponent);
    const header = fixture.debugElement.nativeElement;
    expect(header.clientHeight).toEqual(41);
  });
});
