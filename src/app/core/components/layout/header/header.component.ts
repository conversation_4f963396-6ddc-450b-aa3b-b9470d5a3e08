import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';

import { GeneralDialogData } from '@core/models/modal-data.model';

import { discardChanges } from '@app/store/shared.actions';
import { loggedUserName } from '@app/store/shared.selectors';

import { AuthService } from '@core/services/auth-service/auth.service';
import { UnsavedChangesService } from '@shared/services/unsaved-changes/unsaved-changes.service';

import { GeneralDialogComponent } from '../../general-dialog/general-dialog.component';

import { USER_ROLES } from '@core/models/user-roles.models';

import { getInitials } from './helpers/header.helpers';

import { DIALOG_SIZE_UNSAVED_CHANGES, ICON_ADMIN_PANEL } from '@core/constants/constants';

@Component({
  standalone: false,
  selector: 'layout-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit {
  loggedUserName$: Observable<string | undefined> = this.store.select(loggedUserName);
  hasUnsavedChanges$: Observable<boolean> = this.unsavedChangesService.getHasUnsavedChanges$();

  hasUnsavedChanges!: boolean;
  isHomePage: boolean = false;
  ICON_ADMIN_PANEL: string = ICON_ADMIN_PANEL;
  USER_ROLES = USER_ROLES;
  userInitials = '';

  destroyRef = inject(DestroyRef);

  constructor(
    public dialog: MatDialog,
    private store: Store,
    private authService: AuthService,
    private router: Router,
    private unsavedChangesService: UnsavedChangesService,
  ) {}

  ngOnInit() {
    this.hasUnsavedChanges$.subscribe(hasUnsavedChanges => {
      this.hasUnsavedChanges = hasUnsavedChanges;
    });

    this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.isHomePage = this.router.url === '/';
    });

    this.loggedUserName$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(name => {
      if (name) {
        this.userInitials = getInitials(name);
      }
    });
  }

  logout(): void {
    this.authService.logout();
  }

  handleLogout(): void {
    const handleLogoutAction = this.hasUnsavedChanges
      ? () => {
          this.store.dispatch(discardChanges());
          this.logout();
        }
      : () => this.logout();

    const dialogData: GeneralDialogData = {
      isTitleVisible: false,
      dialogContent: this.hasUnsavedChanges ? 'common.logoutMessage' : 'logout.logoutConfirmationMessage',
      confirmationButtonText: this.hasUnsavedChanges ? 'common.discardChangesAndLogout' : 'common.logout',
      onConfirmCallback: handleLogoutAction,
      shouldShowCancelButton: true,
      isCancelPrimaryButton: this.hasUnsavedChanges,
    };

    this.dialog.open(GeneralDialogComponent, {
      minWidth: DIALOG_SIZE_UNSAVED_CHANGES.minWidth,
      maxWidth: DIALOG_SIZE_UNSAVED_CHANGES.maxWidth,
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
  }
}
