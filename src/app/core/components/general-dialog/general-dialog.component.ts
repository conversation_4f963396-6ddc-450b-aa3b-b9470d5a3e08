// This component is copied also in Pricing Tool. If changes are made, they should be made in both places.
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { GeneralDialogData } from '@core/models/modal-data.model';

@Component({
  standalone: false,
  selector: 'app-general-dialog',
  templateUrl: './general-dialog.component.html',
  styleUrls: ['./general-dialog.component.scss'],
})
export class GeneralDialogComponent implements OnInit {
  dialogConfig!: GeneralDialogData;

  defaultGeneralDialogData: GeneralDialogData = {
    isTitleVisible: true,
    dialogTitle: '',
    dialogContent: '',
    confirmationButtonText: '',
    onConfirmCallback: () => {},
    shouldShowCancelButton: false,
    cancelButtonText: `common.cancel`,
    onCancelCallback: this.onCancel.bind(this),
    isCancelPrimaryButton: false,
  };

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: GeneralDialogData,
    public dialogRef: MatDialogRef<GeneralDialogComponent>,
  ) {}

  ngOnInit(): void {
    this.dialogConfig = {
      ...this.defaultGeneralDialogData,
      ...this.data,
    };
  }

  onCancel() {
    this.dialogRef.close(false);
  }
}
