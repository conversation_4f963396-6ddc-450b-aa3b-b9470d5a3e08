<app-dialog [isTitleVisible]="dialogConfig.isTitleVisible">
  <div *ngIf="dialogConfig.dialogTitle" header>
    {{ dialogConfig.dialogTitle | translate }}
  </div>
  <div
    *ngIf="dialogConfig.dialogContent"
    content
    [innerHtml]="dialogConfig.dialogContent | translate: dialogConfig.translationParams"
  ></div>

  <div actions class="dialog-btns-content-wrapper">
    <!-- It should be an if-else check cause as per today Material doesn't support dynamic setting of mat directive for stroked/rasised, etc. -->
    @if (dialogConfig.isCancelPrimaryButton) {
      @if (dialogConfig.onConfirmCallback && dialogConfig.confirmationButtonText) {
        <button
          [mat-dialog-close]="true"
          mat-stroked-button
          class="secondary-operation-btn"
          color="primary"
          id="confirm-button"
          (click)="dialogConfig.onConfirmCallback()"
        >
          {{ dialogConfig.confirmationButtonText | translate }}
        </button>
      }
      @if (dialogConfig.shouldShowCancelButton && dialogConfig.onCancelCallback && dialogConfig.cancelButtonText) {
        <button mat-raised-button color="primary" id="cancel-button" (click)="dialogConfig.onCancelCallback()">
          {{ dialogConfig.cancelButtonText | translate }}
        </button>
      }
    } @else {
      @if (dialogConfig.onConfirmCallback && dialogConfig.confirmationButtonText) {
        <button
          [mat-dialog-close]="true"
          mat-raised-button
          color="primary"
          id="confirm-button"
          (click)="dialogConfig.onConfirmCallback()"
        >
          {{ dialogConfig.confirmationButtonText | translate }}
        </button>
      }
      @if (dialogConfig.shouldShowCancelButton && dialogConfig.onCancelCallback && dialogConfig.cancelButtonText) {
        <button
          mat-stroked-button
          color="primary"
          class="secondary-operation-btn"
          id="cancel-button"
          (click)="dialogConfig.onCancelCallback()"
        >
          {{ dialogConfig.cancelButtonText | translate }}
        </button>
      }
    }
  </div>
</app-dialog>
