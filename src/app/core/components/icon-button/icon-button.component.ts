import { Component, EventEmitter, Input, Output } from '@angular/core';

import { ICON_PLUS } from '@core/constants/constants';

@Component({
  standalone: false,
  selector: 'app-icon-button',
  templateUrl: './icon-button.component.html',
})
export class IconButtonComponent {
  @Input() label: string = '';
  @Input() iconButton: string = ICON_PLUS;
  @Input() isButtonDisabled: boolean = false;

  @Output() onButtonClick = new EventEmitter<void>();

  handleClick(): void {
    this.onButtonClick.emit();
  }
}
