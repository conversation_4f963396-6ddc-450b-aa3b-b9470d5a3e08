import { Component, OnInit } from '@angular/core';

import { MAIN_ROUTES } from '@core/constants/routes';

@Component({
  standalone: false,
  selector: 'app-error-page',
  templateUrl: './error-page.component.html',
  styleUrls: ['./error-page.component.scss'],
})
export class ErrorPageComponent implements OnInit {
  ROUTES = MAIN_ROUTES;
  errorCode: string = '';
  errorHeading: string = '';
  errorMessage: string = '';
  returnHomeButtonText: string = '';

  ngOnInit(): void {
    const routeState = history.state;

    this.errorCode = routeState.errorCode || 'pageNotFound.text404';
    this.errorHeading = routeState.errorHeading || 'pageNotFound.heading';
    this.errorMessage = routeState.errorMessage || 'pageNotFound.text';
    this.returnHomeButtonText = routeState.returnHomeButtonText || 'pageNotFound.returnHomePageText';
  }
}
