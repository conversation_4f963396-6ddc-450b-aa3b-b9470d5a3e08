<p-toast position="bottom-left" [life]="2000"></p-toast>

<production-header
  [isSideNavShown]="isMainSidenavOpen$ | async"
  [shouldRemoveSidebar]="shouldRemoveSidebar"
  (toggleSidebar)="getSideNavData($event)"
>
</production-header>

<ng-container *ngTemplateOutlet="shouldRemoveSidebar ? withoutSidebar : withSidebar"></ng-container>

<ng-template #withSidebar>
  <ng-container *ngIf="{ isMainSidenavOpen: isMainSidenavOpen$ | async } as sidebarState">
    <mat-sidenav-container class="layout-container" autosize>
      <mat-sidenav
        [ngClass]="sidebarState.isMainSidenavOpen ? 'sidebar' : 'sidebar-collapsed'"
        mode="side"
        [opened]="true"
        id="nav-sidebar"
      >
        <production-sidebar
          [isSideNavShown]="sidebarState.isMainSidenavOpen"
          [routes]="ROUTES"
          [navItems]="NAVITEMS"
        ></production-sidebar>
        <div class="app-version">v{{ appVersion }}</div>
      </mat-sidenav>
      <mat-sidenav-content
        class="main-content"
        [ngClass]="sidebarState.isMainSidenavOpen ? 'main-content' : 'main-content-collapsed'"
      >
        <div
          class="content-overlay"
          *ngIf="sidebarState.isMainSidenavOpen"
          (click)="getSideNavData(sidebarState.isMainSidenavOpen || true)"
        ></div>
        <router-outlet></router-outlet>
      </mat-sidenav-content>
    </mat-sidenav-container>
  </ng-container>
</ng-template>

<ng-template #withoutSidebar>
  <router-outlet></router-outlet>
</ng-template>
