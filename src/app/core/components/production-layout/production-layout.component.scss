@use 'variables';

.sidebar {
  width: variables.$width-sidebar;
  color: variables.$color-text-dark;
  transition: width 100ms ease;
  background-color: variables.$lht-white;
}

.sidebar-collapsed {
  width: 55px;
  color: variables.$color-text-dark;
  transition: width 100ms ease;
  background-color: variables.$lht-white;
}

::ng-deep.mat-drawer-container {
  background-color: variables.$lht-blue-5;
}

.layout-container {
  height: calc(100vh - #{variables.$main-container-height});

  .app-version {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 0;
    color: variables.$color-text-dark;
    font-size: 1.3rem;
    font-style: italic;
    text-align: center;
  }
}

.main-content {
  margin-left: 0 !important;
  padding: 20px 20px 20px calc(variables.$width-sidebar + 20px);
  background-color: variables.$lht-blue-5;
  overflow-y: scroll;
}

.main-content-collapsed {
  padding: 20px 20px 20px calc(variables.$width-sidebar-collapsed + 20px);
}

.toast-container {
  max-height: 150px;
}

::ng-deep.p-toast-message {
  .p-toast-message-icon {
    margin-right: 1rem;

    .p-icon {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

@media screen and (max-width: 1024px) {
  .content-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
  }
}

@media screen and (max-width: 600px) {
  .sidebar-collapsed {
    width: 0;
  }

  .main-content,
  .main-content-collapsed {
    padding: 20px;
  }
}
