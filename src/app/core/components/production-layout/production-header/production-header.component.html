<header class="header">
  <div class="left">
    <button *ngIf="!shouldRemoveSidebar" class="header-image-wrapper" (click)="toggleSideNav()">
      <div class="toggle-img"></div>
    </button>
    <a class="header-image-wrapper" [routerLink]="['']">
      <img class="header-img" src="./../../../../../assets/images/logo.svg" alt="logo" />
    </a>
    <span class="title" id="header-project-name">
      {{ 'common.title' | translate }}
    </span>
    <span class="separator"></span>
    <div class="breadcrumbs-wrapper">
      <production-breadcrumbs></production-breadcrumbs>
    </div>
  </div>
  <div class="right">
    <a class="profile-wrapper">
      <span class="profile-name" id="header-username">{{ loggedUserName$ | async }}</span>
    </a>
    @if ((userRole$ | async) === USER_ROLES.ADMIN) {
      <button
        #tooltip="matTooltip"
        [matTooltip]="'common.adminPanelIconTooltip' | translate"
        class="admin-icon"
        (click)="navigateToAdminPanel()"
      >
        <mat-icon>
          {{ ICON_ADMIN_PANEL }}
        </mat-icon>
      </button>
    }
    <button
      class="header-image-wrapper"
      (click)="handleLogout()"
      id="header-logout-button"
      #tooltip="matTooltip"
      [matTooltip]="'common.logout' | translate"
    >
      <img class="header-img default" alt="logout" src="./../../../../../assets/images/logout.svg" />
      <img class="header-img hover" alt="logout" src="./../../../../../assets/images/logout-hover.svg" />
    </button>
  </div>
</header>
