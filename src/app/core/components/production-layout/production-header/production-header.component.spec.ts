import { TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';

import { ProductionBreadcrumbsComponent } from '@app/core/components/production-layout/production-header/production-breadcrumbs/production-breadcrumbs.component';
import { ProductionHeaderComponent } from '@core/components/production-layout/production-header/production-header.component';

describe('ProductionHeaderComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ProductionHeaderComponent, ProductionBreadcrumbsComponent],
      imports: [TranslateModule.forRoot()],
    }).compileComponents();
  });

  it('Should create the header', () => {
    const fixture = TestBed.createComponent(ProductionHeaderComponent);
    const header = fixture.componentInstance;
    expect(header).toBeTruthy();
  });

  it('The header should be 41 px', () => {
    const fixture = TestBed.createComponent(ProductionHeaderComponent);
    const header = fixture.debugElement.nativeElement;
    expect(header.clientHeight).toEqual(41);
  });
});
