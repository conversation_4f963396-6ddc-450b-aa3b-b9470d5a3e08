import { Component } from '@angular/core';
import { Observable } from 'rxjs';

import { Breadcrumb } from '@core/models/breadcrumb';

import { BreadcrumbService } from '@core/components/production-layout/production-header/production-breadcrumbs/breadcrumbs.service';

@Component({
  standalone: false,
  selector: 'production-breadcrumbs',
  templateUrl: './production-breadcrumbs.component.html',
  styleUrls: ['./production-breadcrumbs.component.scss'],
})
export class ProductionBreadcrumbsComponent {
  breadcrumbs$: Observable<Breadcrumb[]>;

  constructor(private readonly breadcrumbService: BreadcrumbService) {
    this.breadcrumbs$ = this.breadcrumbService.breadcrumbs$;
  }
}
