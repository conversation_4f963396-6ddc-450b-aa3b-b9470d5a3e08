import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { filter } from 'rxjs/operators';
import { ActivatedRouteSnapshot, Data, NavigationEnd, Router } from '@angular/router';

import { Breadcrumb } from '@core/models/breadcrumb';

@Injectable({
  providedIn: 'root',
})
export class BreadcrumbService {
  // Subject emitting the breadcrumb hierarchy
  private readonly _breadcrumbs$ = new BehaviorSubject<Breadcrumb[]>([]);

  // Observable exposing the breadcrumb hierarchy
  readonly breadcrumbs$ = this._breadcrumbs$.asObservable();

  constructor(private router: Router) {
    this.router.events
      .pipe(
        // Filter the NavigationEnd events as the breadcrumb is updated only when the route reaches its end
        filter(event => event instanceof NavigationEnd),
      )
      .subscribe(event => {
        // Construct the breadcrumb hierarchy
        const root = this.router.routerState.snapshot.root;
        const breadcrumbs: Breadcrumb[] = [];
        this.addBreadcrumb(root, breadcrumbs);

        // Emit the new hierarchy
        this._breadcrumbs$.next(breadcrumbs);
      });
  }

  private addBreadcrumb(route: ActivatedRouteSnapshot | null, breadcrumbs: Breadcrumb[]) {
    if (route) {
      const breadcrumbData = this.getResolver(route.data);
      // Add an element for the current route part
      if (breadcrumbData) {
        const breadcrumb = {
          title: this.constructBreadcrumbTitle(route.data, breadcrumbData.title),
          link: '/' + breadcrumbData.link,
          paramValues: {},
        };
        if (breadcrumbData && breadcrumbData.paramKey && breadcrumbData.paramName) {
          breadcrumb.paramValues = {
            [breadcrumbData.paramKey]: route.paramMap.get(breadcrumbData.paramName) as string,
          };
        }

        breadcrumbs.push(breadcrumb);
      }

      // Add another element for the next route part
      this.addBreadcrumb(route.firstChild, breadcrumbs);
    }
  }

  private constructBreadcrumbTitle(data: Data, offerNumber: string): string {
    const quotationDetails = data['quotationDetails'];
    if (!quotationDetails) return offerNumber;

    const { offerNumber: quotationOfferNumber, version, position, scenario } = quotationDetails;
    return `${quotationOfferNumber} V${version} P${position} S${scenario}`;
  }

  // @TODO use this when resolver is added
  private getResolver(data: Data) {
    // The breadcrumb can be defined as a static string or as a function to construct the breadcrumb element out of the route data
    return typeof data['breadcrumb'] === 'function' ? data['breadcrumb'](data) : data['breadcrumb'];
  }
}
