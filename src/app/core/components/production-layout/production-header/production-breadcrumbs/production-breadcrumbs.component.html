<div class="breadcrumbs-container" id="header-breadcrumbs">
  @for (item of breadcrumbs$ | async; track item; let isLast = $last) {
    <ng-container *ngTemplateOutlet="isLast ? lastElement : breadcrumbTemp; context: { breadcrumb: item }">
    </ng-container>
  }
</div>

<ng-template #breadcrumbTemp let-breadcrumb="breadcrumb">
  <a class="breadcrumb-item" [routerLink]="'/' + breadcrumb.link" [title]="breadcrumb.title | translate">
    {{ breadcrumb.title }}
  </a>
  <span> / </span>
</ng-template>

<ng-template #lastElement let-breadcrumb="breadcrumb">
  <span [title]="breadcrumb.title | translate: breadcrumb.paramValues" class="breadcrumb-item last-breadcrumb">
    {{ breadcrumb.title }}
  </span>
</ng-template>
