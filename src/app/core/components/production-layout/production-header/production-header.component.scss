@use 'variables';

.header {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: space-between;
  width: 100%;
  height: 41px;
  padding: 8px 16px;
  color: variables.$lht-white;
  background-color: variables.$lht-blue-100;

  .left,
  .right,
  .profile-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .title {
    font-size: 1.8rem;
  }

  .header-img {
    width: 100%;
    height: 100%;
    vertical-align: middle;
    background-color: variables.$lht-blue-100;

    &.hover {
      display: none;
    }
  }

  .toggle-img {
    width: 100%;
    height: 100%;
    background: url('~assets/images/nav.svg');
    vertical-align: middle;
  }

  .toggle-img:hover {
    background: url('~assets/images/nav-hover.svg');
  }

  .header-image-wrapper {
    width: 24px;
    height: 24px;
    padding: 0;
    border: 0;
    cursor: pointer;
    background-color: variables.$lht-blue-100;

    &:hover {
      fill: variables.$color-text-dark;

      .default {
        display: none;
      }
      .hover {
        display: block;
      }
    }
  }

  .profile-wrapper {
    &:hover {
      .default {
        display: none;
      }

      .hover {
        display: block;
      }
    }

    .profile-name {
      color: variables.$lht-white;
    }
  }

  .separator {
    width: 1px;
    align-self: stretch;
    background-color: variables.$lht-white;
  }

  .admin-icon {
    display: flex;
    padding: 0 !important;
    border: none !important;
    color: variables.$lht-white;
    cursor: pointer;
    background-color: inherit;

    mat-icon {
      font-size: 2.9rem !important;
      line-height: 0.8;

      &:hover {
        color: variables.$lht-blue-semantic-100-info;
      }
    }
  }

  @media only screen and (max-width: 600px) {
    .profile-name {
      display: none;
    }

    .breadcrumbs-wrapper {
      display: none;
    }
  }
}
