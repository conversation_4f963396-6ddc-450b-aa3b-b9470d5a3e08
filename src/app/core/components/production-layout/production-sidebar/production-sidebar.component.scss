@use 'variables';

.side-nav {
  padding-top: 8px;

  .nav-item {
    display: flex;
    align-items: center;
    height: 48px;
    border-left: 2px solid variables.$lht-white;
    color: variables.$color-text-dark;
    cursor: pointer;
  }

  ::ng-deep.disabled-link {
    color: variables.$color-text-disabled-sidenav !important;
    cursor: not-allowed;
    pointer-events: none;
    text-decoration: underline;
  }

  .nav-link {
    width: 100%;
    color: variables.$color-text-dark;
    text-decoration: none;
    background-color: variables.$lht-white;

    .icon {
      width: 24px;
      height: 24px;
      margin: 0 8px;
      background-size: contain;
    }

    .icon-home {
      background-image: url('~assets/images/home-blue.svg');
      background-repeat: no-repeat;
    }

    .icon-pricing-tool {
      background-image: url('~assets/images/icon-pricing-tool.svg');
      background-repeat: no-repeat;
    }

    .icon-right {
      margin: 1.6rem;
    }
  }

  .nav-item:hover {
    background-color: variables.$lht-blue-secondary-radiant-5;
  }

  .nav-item.active {
    color: variables.$lht-white;
    border-color: variables.$lht-blue-100;
    background-color: variables.$lht-blue-secondary-radiant-100;

    .icon-home {
      background-image: url('~assets/images/home-white.svg');
    }

    .icon-pricing-tool {
      background-image: url('~assets/images/icon-pricing-tool-white.svg');
    }
  }
}
