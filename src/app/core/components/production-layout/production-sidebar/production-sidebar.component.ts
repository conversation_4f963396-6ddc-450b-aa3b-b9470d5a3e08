import { Component, Input } from '@angular/core';

import { NavItemConfig } from '@core/models/nav-item-config';

@Component({
  standalone: false,
  selector: 'production-sidebar',
  templateUrl: './production-sidebar.component.html',
  styleUrls: ['./production-sidebar.component.scss'],
})
export class ProductionSidebarComponent {
  @Input() isSideNavShown: boolean | null = true;
  @Input() routes!: any;
  @Input() navItems!: NavItemConfig[];
}
