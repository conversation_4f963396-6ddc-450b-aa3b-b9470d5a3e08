import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';

import { isMainSidebarShown } from '@app/store/shared.selectors';
import { setMainSidebarState } from '@app/store/shared.actions';

import { MAIN_ROUTES } from '@core/constants/routes';
import { navItems } from '@core/constants/nav-items-production';

declare function require(moduleName: string): any;
const { version: appVersion } = require('../../../../../package.json');

@Component({
  standalone: false,
  selector: 'production-layout',
  templateUrl: './production-layout.component.html',
  styleUrls: ['./production-layout.component.scss'],
})
export class ProductionLayoutComponent implements OnInit {
  isMainSidenavOpen$ = this.store.select(isMainSidebarShown);

  appVersion: string = appVersion;
  isSideNavShown: boolean = true;
  shouldRemoveSidebar: boolean = false;
  ROUTES = MAIN_ROUTES;
  //TODO: Change this when the actual new tiles are created
  NAVITEMS = navItems.filter(item => item.title !== 'XXXXXX');

  private destroyRef = inject(DestroyRef);

  constructor(
    private router: Router,
    private store: Store,
  ) {}

  ngOnInit() {
    this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(data => {
      if (data instanceof ActivationEnd) {
        this.setSidebarShowing(data);
      }
    });
  }

  setSidebarShowing(data?: ActivationEnd): void {
    const noSidebarLayout = data?.snapshot?.children[0]?.data['noSidebarLayout'];
    this.shouldRemoveSidebar = noSidebarLayout;
  }

  getSideNavData(isSidenavShown: boolean): void {
    this.store.dispatch(setMainSidebarState({ isMainSidebarShown: isSidenavShown }));
  }
}
