@use 'variables';

.maintenance-page-container {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: calc(100vh - 64px);
  background: variables.$lht-white;

  .maintenance-header {
    padding-top: 25px;
    text-align: center;
  }

  .engine-image-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 800px;
    height: 800px;
    padding: 20px;

    .engine {
      width: 400px;
      height: 400px;
      border: solid 20px variables.$lht-grey-2;
      border-radius: 50%;
      -webkit-animation: spin 5s linear infinite;
      -moz-animation: spin 5s linear infinite;
      animation: spin 5s linear infinite;

      .engine-image {
        width: 100%;
        height: 100%;
      }
    }

    .center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  @-moz-keyframes spin {
    100% {
      -moz-transform: translate(-50%, -50%) rotate(-360deg);
    }
  }
  @-webkit-keyframes spin {
    100% {
      -webkit-transform: translate(-50%, -50%) rotate(-360deg);
    }
  }
  @keyframes spin {
    100% {
      -webkit-transform: translate(-50%, -50%) rotate(-360deg);
      transform: translate(-50%, -50%) rotate(-360deg);
    }
  }
}
