import { Component, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { Subscription, interval } from 'rxjs';

import { EXPIRY_ALERT_ADVANCE_SECONDS } from '@core/constants/constants';

@Component({
  standalone: false,
  selector: 'token-expiration-dialog',
  templateUrl: './token-expiration-dialog.component.html',
  styleUrls: ['./token-expiration-dialog.component.scss'],
})
export class TokenExpirationDialogComponent implements OnInit, OnDestroy {
  totalTime = EXPIRY_ALERT_ADVANCE_SECONDS;
  timeRemaining = EXPIRY_ALERT_ADVANCE_SECONDS;
  timerSubscription!: Subscription;

  constructor(public dialogRef: MatDialogRef<TokenExpirationDialogComponent>) {}

  ngOnInit() {
    this.timerSubscription = interval(1000).subscribe(() => {
      if (this.timeRemaining > 0) {
        this.timeRemaining--;
      } else {
        this.dialogRef.close();
        this.timerSubscription.unsubscribe();
      }
    });
  }

  onContinueClick() {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    this.timerSubscription.unsubscribe();
  }
}
