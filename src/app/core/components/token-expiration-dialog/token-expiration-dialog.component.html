<div class="session-container">
  <div class="session-header-container">
    <h1 mat-dialog-title>{{ 'sessionExpiration.sessionExpiringHeading' | translate }}</h1>

    <div class="timer-container">
      <mat-progress-spinner
        class="timer-spinner"
        mode="determinate"
        [diameter]="40"
        [value]="(timeRemaining / totalTime) * 100"
      >
      </mat-progress-spinner>
      <span class="timer-text">{{ timeRemaining }}</span>
    </div>
  </div>
  <div mat-dialog-content class="session-content">{{ 'sessionExpiration.sessionExpiringText' | translate }}</div>
  <div mat-dialog-actions class="session-btns-container">
    <button
      mat-dialog-close
      mat-flat-button
      color="primary"
      (click)="onContinueClick()"
      >{{ 'sessionExpiration.continue' | translate }}
    </button>
  </div>
</div>
