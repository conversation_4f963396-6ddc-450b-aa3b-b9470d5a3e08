import { HttpEvent, HttpHandlerFn, HttpInterceptorFn, HttpRequest, HttpResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

import { ToastService } from '@shared/services/toast-service/toast.service';

import { HttpMethods } from '../constants/http-methods.constants';

export const toastNotificationInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const toastService = inject(ToastService);

  const excludedUrlsPatterns = [
    /^https:\/\/core-calculation-service-(?:develop|stage)?\.apps\.az\.lhtcloud\.com\/api\/users\/details$/,
  ];
  const userDataDeletePattern = /\/api\/admin\/user-data\/\d+$/;
  let currentEvent: HttpEvent<any>;

  const isUrlExcluded = (url: string): boolean => {
    return excludedUrlsPatterns.some(pattern => pattern.test(url));
  };

  return next(request).pipe(
    tap((event: HttpEvent<any>) => {
      currentEvent = event;

      if (
        request.method === HttpMethods.PUT &&
        !isUrlExcluded(request.url) &&
        event instanceof HttpResponse &&
        event.status === 200
      ) {
        toastService.showSuccess('toastMessages.success', 'toastMessages.savedChangesMessage');
      }

      if (
        request.method === HttpMethods.DELETE &&
        userDataDeletePattern.test(request.url) &&
        event instanceof HttpResponse &&
        event.status === 200
      ) {
        toastService.showSuccess('toastMessages.success', 'toastMessages.userDeletedSuccessMessage');
      }
    }),
    catchError(_ => {
      if (request.method === HttpMethods.DELETE && userDataDeletePattern.test(request.url)) {
        toastService.showDanger('toastMessages.userDeletionFailedMessage', 'toastMessages.error');
      }

      return of(currentEvent);
    }),
  );
};
