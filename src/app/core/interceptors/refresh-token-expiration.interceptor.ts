import { HttpEvent, HttpHandlerFn, HttpInterceptorFn, HttpRequest, HttpResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable, Subscription, filter, switchMap, tap, timer } from 'rxjs';

import { AuthService } from '../services/auth-service/auth.service';

import { TokenExpirationDialogComponent } from '../components/token-expiration-dialog/token-expiration-dialog.component';

import { EXPIRY_ALERT_ADVANCE_SECONDS, SECONDS_TO_MILLISECONDS } from '../constants/constants';

export const refreshTokenExpirationInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const authService = inject(AuthService);
  const dialog = inject(MatDialog);

  let dialogTimeoutSub: Subscription | undefined;
  let logoutTimeoutSub: Subscription | undefined;
  let isDialogOpen = false;

  const handleTokenExpiration = (): void => {
    const expirationTime =
      (authService.getSecondsUntilTokenExpiration() - EXPIRY_ALERT_ADVANCE_SECONDS) * SECONDS_TO_MILLISECONDS;
    dialogTimeoutSub?.unsubscribe();

    if (expirationTime > 0 && !isDialogOpen) {
      showExpirationDialog(expirationTime);
    }
  };

  const showExpirationDialog = (expirationTime: number): void => {
    dialogTimeoutSub = timer(expirationTime)
      .pipe(
        tap(() => (isDialogOpen = true)),
        switchMap(() => {
          const dialogRef = dialog.open(TokenExpirationDialogComponent, { disableClose: true });
          handleLogoutTimeout();
          return dialogRef.afterClosed();
        }),
      )
      .subscribe(() => handleDialogResult());
  };

  const handleLogoutTimeout = (): void => {
    logoutTimeoutSub?.unsubscribe();
    logoutTimeoutSub = timer(EXPIRY_ALERT_ADVANCE_SECONDS * SECONDS_TO_MILLISECONDS)
      .pipe(filter(() => isDialogOpen))
      .subscribe(() => {
        authService.logout();
      });
  };

  const handleDialogResult = (): void => {
    isDialogOpen = false;
    authService.updateToken();
    logoutTimeoutSub?.unsubscribe();
  };

  return next(request).pipe(
    tap(event => {
      if (event instanceof HttpResponse) {
        handleTokenExpiration();
      }
    }),
  );
};
