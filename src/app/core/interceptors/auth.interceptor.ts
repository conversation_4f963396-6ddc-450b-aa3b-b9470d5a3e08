import { Http<PERSON>vent, HttpHandlerFn, HttpInterceptorFn, HttpRequest, HttpResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, finalize, tap } from 'rxjs';

import { LocalStorageService } from '@core/services/local-storage/local-storage.service';
import { AuthService } from '../services/auth-service/auth.service';
import { LoadingWrapperService } from '../services/loading-wrapper-service/loading-wrapper.service';

import { AUTOCOMPLETE_FILTERS_ROUTE } from '@core/constants/constants';

export const authInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const loadingService = inject(LoadingWrapperService);
  const localStorageService = inject(LocalStorageService);
  const authService = inject(AuthService);

  const token = localStorageService.getData('access_token') as string;

  const isPollingRequest = request.url.includes('calculation-state') || request.url.includes('copy-state');

  const showLoading = (): void => {
    if (!isPollingRequest) {
      loadingService.showSpinner();
    }
  };

  const hideLoading = (): void => {
    if (!isPollingRequest) {
      loadingService.hideSpinner();
    }
  };

  //Prevent showing loading spinner for offer numbers autocomplete request
  if (!request.url.includes(AUTOCOMPLETE_FILTERS_ROUTE) && !isPollingRequest) {
    showLoading();
  }

  const clonedRequest = request.clone({
    setHeaders: {
      Authorization: `Bearer ${token}`,
    },
  });

  return next(clonedRequest).pipe(
    tap(event => {
      if (event instanceof HttpResponse) {
        authService.updateToken();
      }
    }),
    finalize(() => hideLoading()),
  );
};
