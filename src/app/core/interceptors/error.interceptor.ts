import { HttpErrorResponse, HttpEvent, HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { LhtLoadingService } from 'cp-lht-spinner';
import { EMPTY, Observable, catchError, from, mergeMap, throwError } from 'rxjs';

import {
  ErrorCodes,
  NavigationToErrorPageService,
} from '@shared/services/navigation-service/navigation-to-error-page.service';
import { AuthService } from '../services/auth-service/auth.service';
import { LocalStorageService } from '../services/local-storage/local-storage.service';
import { ToastService } from '@shared/services/toast-service/toast.service';

export const errorInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const localStorageService = inject(LocalStorageService);
  const toastService = inject(ToastService);
  const loadingService = inject(LhtLoadingService);
  const navigateToErrorPageService = inject(NavigationToErrorPageService);

  const handleUnauthorized = (): Observable<HttpEvent<any>> => {
    if (!authService.isRefreshTokenExpired()) {
      return tryRefreshToken();
    }
    authService.logout();
    return EMPTY;
  };

  const tryRefreshToken = (): Observable<HttpEvent<any>> => {
    return from(authService.updateToken()).pipe(
      mergeMap(() => retryRequestWithNewToken()),
      catchError(() => {
        authService.checkIfTokenHasExpired();
        return EMPTY;
      }),
    );
  };

  const retryRequestWithNewToken = (): Observable<HttpEvent<any>> => {
    const newToken = localStorageService.getData('access_token') as string;

    if (newToken && newToken !== request.headers.get('Authorization')) {
      const clonedRequest = request.clone({
        setHeaders: {
          Authorization: `Bearer ${newToken}`,
        },
      });
      return next(clonedRequest);
    }

    authService.checkIfTokenHasExpired();
    return EMPTY;
  };

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      const isWorkscopeSummaryRequest = request.url.includes('/summary') && request.method === 'POST';
      loadingService.hide();
      switch (error.status) {
        case 400:
          if (request.method === 'GET' || isWorkscopeSummaryRequest) {
            navigateToErrorPageService.navigateToErrorPage(ErrorCodes.PAGE_NOT_FOUND, router);
          } else {
            toastService.showDanger(error.error.error.detail, 'toastMessages.error');
          }
          break;
        case 401:
          return handleUnauthorized();
        case 404:
          navigateToErrorPageService.navigateToErrorPage(ErrorCodes.PAGE_NOT_FOUND, router);
          break;
        default:
          if (error.status >= 500) {
            navigateToErrorPageService.navigateToErrorPage(ErrorCodes.SERVER_ERROR, router);
          } else {
            toastService.showDanger(error.error.error.detail, 'toastMessages.error');
          }
      }

      return throwError(() => error); // catchError must return an observable or throw an error
    }),
  );
};
