export interface AppConfig {
  readonly production: boolean;
  readonly baseApiPath: string;
  readonly keycloakClientId: string;
  readonly keycloakBaseUrl: string;
  readonly keycloakRealm: string;
  readonly keycloakRedirectUri: string;
  readonly 'mfe-coca-pricing-tool': string;
  readonly 'mfe-coca-admin-panel': string;
  readonly 'mfe-coca-project-list-ui': string;
  readonly 'mfe-coca-cost-module-frontend': string;
}

export interface AppMaintenanceConfig {
  readonly isUnderMaintenance: boolean;
}

// Use names of each remote which are listed in their package.json
// Same are used as keys in assets config
export enum ApplicationNames {
  HOST = 'coca-ui-host-app',
  PRICING_TOOL = 'coca-pricing-tool',
  ADMIN_PANEL = 'coca-admin-panel',
  PROJECT_LIST = 'coca-project-list-ui',
  COST_MODULE = 'coca-cost-module-frontend',
}
