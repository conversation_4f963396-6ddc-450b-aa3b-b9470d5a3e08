import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

import {
  ErrorCodes,
  NavigationToErrorPageService,
} from '@shared/services/navigation-service/navigation-to-error-page.service';

import { userDetails } from '@app/store/shared.selectors';

import { MAIN_ROUTES } from '@core/constants/routes';

export const SpecialRoutesGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
): Observable<boolean | UrlTree> | Promise<boolean> | boolean => {
  const store = inject(Store);
  const router = inject(Router);
  const navigationService = inject(NavigationToErrorPageService);

  const isContactAdministratorRoute = route.routeConfig?.path === MAIN_ROUTES.contactAdministrator;
  const isErrorPageRoute = route.routeConfig?.path === MAIN_ROUTES.errorPage;

  return store.select(userDetails).pipe(
    switchMap(userDetails => {
      if (isContactAdministratorRoute) {
        if (userDetails?.permissions && userDetails.permissions.length) {
          navigationService.navigateToErrorPage(ErrorCodes.PAGE_NOT_FOUND, router);
          return of(false);
        }

        return of(true);
      } else if (isErrorPageRoute) {
        const errorState = router.getCurrentNavigation()?.extras?.state;
        if (!errorState || !errorState['errorCode']) {
          navigationService.navigateToErrorPage(ErrorCodes.PAGE_NOT_FOUND, router);
          return of(false);
        }
        return of(true);
      }
      return of(true);
    }),
    catchError(() => {
      isErrorPageRoute || isContactAdministratorRoute
        ? navigationService.navigateToErrorPage(ErrorCodes.PAGE_NOT_FOUND, router)
        : router.navigate([MAIN_ROUTES.contactAdministrator]);

      return of(false);
    }),
  );
};
