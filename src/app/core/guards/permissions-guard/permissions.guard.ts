import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

import { userDetails } from '@app/store/shared.selectors';

import { MAIN_ROUTES } from '@core/constants/routes';

export const PermissionsGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
): Observable<boolean | UrlTree> | Promise<boolean> | boolean => {
  const store = inject(Store);
  const router = inject(Router);
  const isContactAdministratorRoute = route.routeConfig?.path === MAIN_ROUTES.contactAdministrator;

  return store.select(userDetails).pipe(
    switchMap(userDetails => {
      if (userDetails?.permissions && userDetails.permissions.length && !isContactAdministratorRoute) {
        return of(true);
      } else {
        return router.navigate([MAIN_ROUTES.contactAdministrator]).then(() => false);
      }
    }),
    catchError(() => {
      return router.navigate([MAIN_ROUTES.contactAdministrator]).then(() => false);
    }),
  );
};
