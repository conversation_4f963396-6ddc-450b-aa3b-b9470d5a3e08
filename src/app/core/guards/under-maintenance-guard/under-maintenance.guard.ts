import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { ConfigService } from '@core/services/config-service/config.service';
import { CurrentUserService } from '@core/services/current-user-service/current-user.service';
import {
  ErrorCodes,
  NavigationToErrorPageService,
} from '@shared/services/navigation-service/navigation-to-error-page.service';

import { MAIN_ROUTES } from '@core/constants/routes';

export const MaintenanceGuard: CanActivateFn = (route, state) => {
  const configService = inject(ConfigService);
  const navigationService = inject(NavigationToErrorPageService);
  const router = inject(Router);
  const currentUserService = inject(CurrentUserService);

  const isUnderMaintenance = configService.isUnderMaintenance();
  const isMaintenanceRoute = route.routeConfig?.path === MAIN_ROUTES.maintenance;
  const isUserAdmin = currentUserService.isAdmin();

  if (!isUnderMaintenance && isMaintenanceRoute) {
    navigationService.navigateToErrorPage(ErrorCodes.PAGE_NOT_FOUND, router);
    return false;
  }

  if (isUnderMaintenance && !isMaintenanceRoute && !isUserAdmin) {
    router.navigate([MAIN_ROUTES.maintenance]);
    return false;
  }

  return true;
};
