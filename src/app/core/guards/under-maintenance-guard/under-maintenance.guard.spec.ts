import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';

import { underMaintenanceGuard } from './under-maintenance.guard';

describe('underMaintenanceGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) =>
    TestBed.runInInjectionContext(() => underMaintenanceGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
