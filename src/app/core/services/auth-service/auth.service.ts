import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { AngularRolesBasedAuthorisationService } from 'angular-roles-based-authorisation';
import { KeycloakServiceDefault } from 'virava';

import { UserDetailsResponse } from '@openapi/models';
import { UserService } from '@openapi/services';

import { setUserDetails } from 'app/store/shared.actions';

import { ConfigService } from '../config-service/config.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  viravaService!: KeycloakServiceDefault;

  constructor(
    private appConfigService: ConfigService,
    private store: Store,
    private userService: UserService,
    private angularRolesBasedAuthorisationService: AngularRolesBasedAuthorisationService,
  ) {}

  init() {
    const authConfig = this.appConfigService.getKeycloakConfig();

    this.viravaService = new KeycloakServiceDefault();
    this.viravaService.init(authConfig);
    return this.viravaService.login().then(_ => {
      return this.getUserDetails()
        .then((userDetails: UserDetailsResponse | undefined) => {
          if (userDetails && userDetails.data && userDetails.data.role) {
            this.angularRolesBasedAuthorisationService.setUserRoles([userDetails.data.role]);
            this.store.dispatch(setUserDetails({ userDetails: userDetails.data }));
          }
        })
        .catch(_ => {
          //TODO: How to handle if some of these requests fails
        });
    });
  }

  login() {
    return this.viravaService.login();
  }

  logout() {
    this.viravaService.logout(this.viravaService.config.redirectUri);
  }

  getUserDetails() {
    return this.userService.getOrCreateUserDetails().toPromise();
  }

  updateToken() {
    return this.viravaService?.updateToken();
  }

  checkIfTokenHasExpired() {
    this.viravaService?.checkIfTokenHasExpired();
  }

  isRefreshTokenExpired() {
    return this.viravaService?.isRefreshTokenExpired();
  }

  getSecondsUntilTokenExpiration(): number {
    return this.viravaService?.getSecondsUntilTokenExpiration();
  }
}
