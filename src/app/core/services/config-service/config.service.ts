// Exposed for consumption from MFEs
import { Injectable } from '@angular/core';
import { KeycloakConfigDefault } from 'virava/dist/src/auth/keycloak/default';

import { AppConfig, AppMaintenanceConfig } from '@core/models/config.models';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private _maintenanceConfig!: AppMaintenanceConfig;
  private _hostConfig: AppConfig = {} as AppConfig;
  private _configPath = 'assets/config/config.json';
  private _maintenanceConfigPath = 'assets/config-maintenance/config-maintenance.json';

  public async loadHostConfig(): Promise<void> {
    this._hostConfig = await this.loadConfig();
  }

  public getHostConfig(): AppConfig {
    return this._hostConfig;
  }

  public getRemoteAppUrl(appName: string): string {
    const configSearchParam: string = `${'mfe-' + appName}`;
    return this._hostConfig[configSearchParam as keyof AppConfig] as string;
  }

  public isUnderMaintenance(): boolean {
    return this._maintenanceConfig.isUnderMaintenance;
  }

  public getKeycloakConfig(): KeycloakConfigDefault {
    return {
      clientId: this._hostConfig.keycloakClientId,
      baseUrl: this._hostConfig.keycloakBaseUrl,
      realm: this._hostConfig.keycloakRealm,
      redirectUri: this._hostConfig.keycloakRedirectUri,
    };
  }

  public isProductionMode(): boolean {
    return this._hostConfig.production;
  }

  public async loadMaintenanceConfig(): Promise<void> {
    const response = await fetch(this._maintenanceConfigPath);
    this._maintenanceConfig = await response.json();
  }

  private async loadConfig(): Promise<AppConfig> {
    const response = await fetch(this._configPath);

    if (response.ok) {
      return await response.json();
    } else {
      throw new Error(`Unable to load config file for application`);
    }
  }
}
