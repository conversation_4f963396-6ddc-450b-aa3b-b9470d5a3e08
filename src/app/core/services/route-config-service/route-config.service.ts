import { loadRemoteModule } from '@angular-architects/module-federation';
import { Injectable } from '@angular/core';
import { Router, Routes } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AuthorisationGuard } from 'angular-roles-based-authorisation';
import { firstValueFrom } from 'rxjs';

import { ApplicationNames } from '@core/models/config.models';
import { USER_ROLES } from '@core/models/user-roles.models';

import { PermissionsGuard } from '@core/guards/permissions-guard/permissions.guard';
import { SpecialRoutesGuard } from '@core/guards/special-routes-guard/special-routes.guard';
import { MaintenanceGuard } from '@core/guards/under-maintenance-guard/under-maintenance.guard';
import { ConfigService } from '@core/services/config-service/config.service';
import { CurrentUserService } from '@core/services/current-user-service/current-user.service';

import { ContactAdministratorComponent } from '@core/components/contact-administrator/contact-administrator.component';
import { ErrorPageComponent } from '@core/components/error-page/error-page.component';
import { MaintenancePageComponent } from '@core/components/maintenance-page/maintenance-page.component';

import { MAIN_ROUTES } from '@core/constants/routes';

@Injectable({ providedIn: 'root' })
export class RouteConfigService {
  constructor(
    private router: Router,
    private configService: ConfigService,
    private translate: TranslateService,
  ) {
    // Check if we need this or we can simply hardcode the Home title bellow
    firstValueFrom(translate.onLangChange).then(() => {
      this.setRoutes();
    });
  }

  public setRoutes(): void {
    const isProduction = this.configService.isProductionMode();

    const routes: Routes = [
      {
        path: '',
        resolve: [CurrentUserService],
        data: { noSidebarLayout: false },
        children: [
          ...(isProduction ? this.getProductionRoutes() : this.getDevelopmentRoutes()),
          {
            path: MAIN_ROUTES.contactAdministrator,
            component: ContactAdministratorComponent,
            data: {
              breadcrumb: { title: `${this.translate.instant('common.home')}`, link: MAIN_ROUTES.home },
              noSidebarLayout: true,
            },
            canActivate: [SpecialRoutesGuard],
          },
          {
            path: MAIN_ROUTES.errorPage,
            component: ErrorPageComponent,
            data: { noSidebarLayout: true },
            canActivate: [SpecialRoutesGuard],
          },
          {
            path: MAIN_ROUTES.maintenance,
            component: MaintenancePageComponent,
            data: { noSidebarLayout: true },
            canActivate: [MaintenanceGuard, PermissionsGuard],
          },
          // 'angular-roles-based-authorisation' redirects automatically to a 'no-access' route if the user doesn't have the required role; we need to redirect to the home page instead
          {
            path: MAIN_ROUTES.noAccess,
            redirectTo: MAIN_ROUTES.home,
          },
          {
            path: '**',
            redirectTo: MAIN_ROUTES.errorPage,
          },
        ],
      },
    ];

    this.router.resetConfig(routes);
  }

  private getDevelopmentRoutes(): Routes {
    return [
      {
        path: MAIN_ROUTES.home,
        redirectTo: MAIN_ROUTES.projectList,
        pathMatch: 'full',
      },
      {
        path: MAIN_ROUTES.pricingTool,
        canActivate: [PermissionsGuard, MaintenanceGuard],
        data: {
          breadcrumb: {
            title: `${this.translate.instant('common.home')}`,
            link: MAIN_ROUTES.home,
            noSidebarLayout: false,
          },
        },
        loadChildren: () =>
          loadRemoteModule({
            type: 'module',
            remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.PRICING_TOOL)}/remoteEntry.js`,
            exposedModule: './PricingTool',
          })
            .then(m => m.PricingToolModule)
            .catch(err => console.error('Error loading remote module', err)),
      },
      {
        path: MAIN_ROUTES.adminPanel,
        canActivate: [AuthorisationGuard, MaintenanceGuard],
        data: {
          breadcrumb: {
            title: `${this.translate.instant('common.home')}`,
            link: MAIN_ROUTES.home,
            noSidebarLayout: false,
          },
          roles: USER_ROLES.ADMIN,
        },
        loadChildren: () =>
          loadRemoteModule({
            type: 'module',
            remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.ADMIN_PANEL)}/remoteEntry.js`,
            exposedModule: './AdminPanel',
          })
            .then(m => m.AdminPanelModule)
            .catch(err => console.error('Error loading remote module', err)),
      },
      {
        path: MAIN_ROUTES.costModule,
        // canActivate: [AuthorisationGuard, MaintenanceGuard],
        loadChildren: () =>
          loadRemoteModule({
            type: 'module',
            remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.COST_MODULE)}/remoteEntry.js`,
            exposedModule: './routes',
          })
            .then(m => m.COST_MODULE_ROUTES)
            .catch(err => console.error('Error loading remote module', err)),
      },
      //TODO: Update once the Project list app is available
      {
        path: MAIN_ROUTES.projectList,
        // canActivate: [AuthorisationGuard, MaintenanceGuard],
        data: {
          breadcrumb: {
            title: `${this.translate.instant('common.projects')}`,
            link: MAIN_ROUTES.projectList,
            noSidebarLayout: false,
          },
        },
        children: [
          {
            path: '',
            loadChildren: () =>
              loadRemoteModule({
                type: 'module',
                remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.PROJECT_LIST)}/remoteEntry.js`,
                exposedModule: './routes',
              }).then(m => m.PROJECT_LIST_ROUTES),
          },
          {
            path: MAIN_ROUTES.costModule,
            loadChildren: () =>
              loadRemoteModule({
                type: 'module',
                remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.COST_MODULE)}/remoteEntry.js`,
                exposedModule: './routes',
              }).then(m => m.COST_MODULE_ROUTES),
          },
        ],
      },
    ];
  }

  private getProductionRoutes(): Routes {
    return [
      {
        path: MAIN_ROUTES.home,
        loadChildren: () => import('@app/modules/home/<USER>').then(m => m.HomeModule),
        data: { noSidebarLayout: true },
        canActivate: [PermissionsGuard, MaintenanceGuard],
      },
      {
        path: MAIN_ROUTES.pricingTool,
        canActivate: [PermissionsGuard, MaintenanceGuard],
        data: {
          breadcrumb: {
            title: `${this.translate.instant('common.home')}`,
            link: MAIN_ROUTES.home,
            noSidebarLayout: false,
          },
        },
        loadChildren: () =>
          loadRemoteModule({
            type: 'module',
            remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.PRICING_TOOL)}/remoteEntry.js`,
            exposedModule: './PricingTool',
          })
            .then(m => m.PricingToolModule)
            .catch(err => console.error('Error loading remote module', err)),
      },
      {
        path: MAIN_ROUTES.adminPanel,
        canActivate: [AuthorisationGuard, MaintenanceGuard],
        data: {
          breadcrumb: {
            title: `${this.translate.instant('common.home')}`,
            link: MAIN_ROUTES.home,
            noSidebarLayout: false,
          },
          roles: USER_ROLES.ADMIN,
        },
        loadChildren: () =>
          loadRemoteModule({
            type: 'module',
            remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.ADMIN_PANEL)}/remoteEntry.js`,
            exposedModule: './AdminPanel',
          })
            .then(m => m.AdminPanelModule)
            .catch(err => console.error('Error loading remote module', err)),
      },
    ];
  }
}
