import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';

import { UserDetails } from '@openapi/models';
import { USER_ROLES } from '@core/models/user-roles.models';

import { userDetails } from 'app/store/shared.selectors';

@Injectable({
  providedIn: 'root',
})
export class CurrentUserService {
  userDetails: UserDetails | null = null;
  destroyRef = inject(DestroyRef);

  constructor(private store: Store) {
    this.store
      .select(userDetails)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(user => (this.userDetails = user));
  }

  resolve() {
    return !!this.userDetails;
  }

  getUserDetails(): UserDetails | null {
    return this.userDetails;
  }

  isAdmin(): boolean {
    return this.userDetails?.role === USER_ROLES.ADMIN;
  }
}
