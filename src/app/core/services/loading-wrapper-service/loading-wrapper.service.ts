import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LhtLoadingService } from 'cp-lht-spinner';
import { Subscription, catchError, first, of, tap, timer } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoadingWrapperService {
  private timerSubscription: Subscription | null = null;
  private messages: string[] = [];
  private messageIndex: number = 0;
  private messageKeys: string[] = [
    'common.loaderText',
    'common.loaderTextExtended',
    'common.loaderTextExtended2',
    'common.loaderTextExtended3',
  ];
  private outgoingRequestsDuringLoading: number = 1;

  constructor(
    private loadingService: LhtLoadingService,
    private translateService: TranslateService,
  ) {
    this.loadTranslations();
  }

  showSpinner(): void {
    if (!this.messages.length) {
      this.translateService.onLangChange.pipe(first()).subscribe({
        next: () => this.showSpinner(),
      });
      return;
    }

    this.messageIndex = 0;
    // Show only one spinner if there are multiple requests (e.g. polling requests)
    if (this.outgoingRequestsDuringLoading === 1) {
      this.loadingService.show(this.messages[this.messageIndex]);
      this.startTimer();
    }
    this.outgoingRequestsDuringLoading++;
  }

  hideSpinner(): void {
    this.outgoingRequestsDuringLoading = 1;
    this.loadingService.hide();
    this.stopTimer();
  }

  private loadTranslations(): void {
    this.translateService
      .get(this.messageKeys)
      .pipe(
        tap(translations => {
          this.messages = this.messageKeys.map(key => translations[key]);
        }),
        catchError(error => {
          console.error('Error fetching translations:', error);
          this.messages = [];
          return of(null);
        }),
      )
      .subscribe();
  }

  private startTimer(): void {
    this.stopTimer();
    this.timerSubscription = timer(10000, 10000).subscribe(() => {
      this.messageIndex = (this.messageIndex + 1) % this.messages.length;
      this.loadingService.hide();
      this.loadingService.show(this.messages[this.messageIndex]);
    });
  }

  private stopTimer(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
      this.timerSubscription = null;
    }
  }
}
