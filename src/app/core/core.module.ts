import { CommonModule } from '@angular/common';
import { HttpClient, provideHttpClient, withInterceptors } from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { AngularRolesBasedAuthorisationService } from 'angular-roles-based-authorisation';
import { LhtSpinnerModule } from 'cp-lht-spinner';
import { providePrimeNG } from 'primeng/config';
import { firstValueFrom } from 'rxjs';

import { ApiConfiguration } from '@openapi/api-configuration';

import { SharedModule } from '@shared/shared.module';

import { AuthService } from './services/auth-service/auth.service';
import { ConfigService } from './services/config-service/config.service';
import { RouteConfigService } from './services/route-config-service/route-config.service';

import { authInterceptor } from './interceptors/auth.interceptor';
import { errorInterceptor } from './interceptors/error.interceptor';
import { refreshTokenExpirationInterceptor } from './interceptors/refresh-token-expiration.interceptor';
import { toastNotificationInterceptor } from './interceptors/toast-notifications.interceptor';

import { ContactAdministratorComponent } from './components/contact-administrator/contact-administrator.component';
import { DialogComponent } from './components/dialog/dialog.component';
import { ErrorPageComponent } from './components/error-page/error-page.component';
import { GeneralDialogComponent } from './components/general-dialog/general-dialog.component';
import { IconButtonComponent } from './components/icon-button/icon-button.component';
import { HeaderComponent } from './components/layout/header/header.component';
import { LayoutComponent } from './components/layout/layout.component';
import { SidebarComponent } from './components/layout/sidebar/sidebar.component';
import { MaintenancePageComponent } from './components/maintenance-page/maintenance-page.component';
import { ProductionBreadcrumbsComponent } from './components/production-layout/production-header/production-breadcrumbs/production-breadcrumbs.component';
import { ProductionHeaderComponent } from './components/production-layout/production-header/production-header.component';
import { ProductionLayoutComponent } from './components/production-layout/production-layout.component';
import { ProductionSidebarComponent } from './components/production-layout/production-sidebar/production-sidebar.component';
import { TokenExpirationDialogComponent } from './components/token-expiration-dialog/token-expiration-dialog.component';

import PrimeNgPreset from 'assets/themes/primeng-preset';

const initializeApp = (
  appConfigService: ConfigService,
  apiConfiguration: ApiConfiguration,
  authService: AuthService,
  translate: TranslateService,
) => {
  return async () => {
    await appConfigService.loadHostConfig();
    await appConfigService.loadMaintenanceConfig();
    apiConfiguration.rootUrl = appConfigService.getHostConfig().baseApiPath;
    translate.setDefaultLang('en');
    translate.use('en');

    return firstValueFrom(translate.onLangChange).then(() => {
      return authService.init();
    });
  };
};

const setupRoutes = (routeConfigService: RouteConfigService) => {
  return () => routeConfigService.setRoutes();
};
@NgModule({
  declarations: [
    ContactAdministratorComponent,
    HeaderComponent,
    LayoutComponent,
    SidebarComponent,
    ProductionLayoutComponent,
    ProductionHeaderComponent,
    ProductionBreadcrumbsComponent,
    ProductionSidebarComponent,
    ErrorPageComponent,
    MaintenancePageComponent,
    TokenExpirationDialogComponent,
    GeneralDialogComponent,
    DialogComponent,
    IconButtonComponent,
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    RouterModule,
    TranslateModule.forRoot({
      loader: { provide: TranslateLoader, useFactory: HttpLoaderFactory, deps: [HttpClient] },
    }),
    SharedModule,
    LhtSpinnerModule.forRoot({}),
  ],
  providers: [
    ConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      deps: [ConfigService, ApiConfiguration, AuthService, TranslateService, AngularRolesBasedAuthorisationService],
      multi: true,
    },
    RouteConfigService,
    { provide: APP_INITIALIZER, useFactory: setupRoutes, deps: [RouteConfigService], multi: true },
    provideHttpClient(
      withInterceptors([
        authInterceptor,
        toastNotificationInterceptor,
        refreshTokenExpirationInterceptor,
        errorInterceptor,
      ]),
    ),
    providePrimeNG({ theme: { preset: PrimeNgPreset, options: { darkModeSelector: false } } }),
  ],
  exports: [ContactAdministratorComponent, HeaderComponent, LayoutComponent, ProductionLayoutComponent],
})
export class CoreModule {}

export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http);
}
