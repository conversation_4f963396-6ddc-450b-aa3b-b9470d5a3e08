import { MAIN_ROUTES } from '@core/constants/routes';

import { NavItemConfig } from '../models/nav-item-config';

import {
  PATH_ADMIN_PANEL_ICON,
  PATH_PRICING_TOOL_ICON,
  PATH_PROJECTS_ICON,
} from '@shared/constants/svg-paths.constants';

export const navItems: NavItemConfig[] = [
  {
    title: 'common.projects',
    route: MAIN_ROUTES.projectList,
    iconPath: PATH_PROJECTS_ICON,
  },
  {
    title: 'common.pricingTool',
    route: MAIN_ROUTES.pricingTool,
    iconPath: PATH_PRICING_TOOL_ICON,
  },
  {
    title: 'common.configuration',
    route: MAIN_ROUTES.adminPanel,
    iconPath: PATH_ADMIN_PANEL_ICON,
  },
  {
    title: 'common.costModule',
    route: MAIN_ROUTES.costModule,
    iconPath: PATH_PRICING_TOOL_ICON,
  },
];
