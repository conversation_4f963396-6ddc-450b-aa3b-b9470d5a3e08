import { MAIN_ROUTES } from '@core/constants/routes';

import { NavItemConfig } from '../models/nav-item-config';

export const navItems: NavItemConfig[] = [
  {
    title: 'common.home',
    cssClass: 'icon-home icon',
    route: MAIN_ROUTES.home,
  },
  {
    title: 'common.pricingTool',
    cssClass: 'icon-pricing-tool icon',
    route: MAIN_ROUTES.pricingTool,
    description: 'Go to Pricing Tool',
  },
  {
    title: 'XXXXXX',
    cssClass: 'icon-capability-search icon',
    route: '#',
    description: 'Work in progress',
  },
  {
    title: 'XXXXXX',
    cssClass: 'icon-authority-coordinator icon',
    route: '#',
    description: 'Work in progress',
  },
  {
    title: 'XXXXXX',
    cssClass: 'icon-open-issues icon',
    route: '#',
    description: 'Work in progress',
  },
];
