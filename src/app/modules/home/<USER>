import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { SharedModule } from '@app/shared/shared.module';
import { HomeRoutingModule } from './home-routing.module';

import { CardBoxComponent } from './pages/home/<USER>/card-box/card-box.component';
import { HomeComponent } from './pages/home/<USER>';

@NgModule({
  declarations: [HomeComponent, CardBoxComponent],
  imports: [CommonModule, HomeRoutingModule, SharedModule, TranslateModule],
  exports: [HomeComponent],
})
export class HomeModule {}
