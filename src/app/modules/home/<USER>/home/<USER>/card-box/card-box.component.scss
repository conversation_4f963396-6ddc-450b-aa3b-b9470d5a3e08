@use 'variables';

.box-wrapper {
  position: relative;
  display: flex;
  width: 300px;
  height: 250px;
  color: variables.$lht-blue-100;
  cursor: pointer;
  background-color: variables.$lht-white;

  &:hover > .default {
    opacity: 0;
  }

  &:hover > .hover {
    opacity: 1;
  }

  .icon {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    background-size: cover;
  }

  .default {
    margin: 0 auto;
    transition: ease-out 0.3s;
    align-self: center;

    h4 {
      padding: 1.25rem 1rem;
      font-size: 2rem;
      text-align: center;
    }

    .icon-pricing-tool {
      background-image: url('../../../../../../../assets/images/icon-pricing-tool.svg');
    }

    .icon-open-issues {
      background-image: url('../../../../../../../assets//images/icon-open-issues.svg');
    }

    .icon-capability-search {
      background-image: url('../../../../../../../assets/images/icon-capability-search.svg');
      background-size: contain;
    }

    .icon-authority-coordinator {
      background-image: url('../../../../../../../assets/images/icon-authority-coordinator.svg');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .icon-arrow-right {
    width: 20px;
    height: 20px;
    background-image: url('../../../../../../../assets/images/icon-arrow-right.svg');
  }

  .hover {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 1rem 0.875rem 1.375rem 0.875rem;
    font-size: 1.4rem;
    opacity: 0;
    transition: ease-out 0.3s;

    .bottom-text {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }
  }

  &.bg-dark {
    width: 250px;
    height: 200px;
    color: variables.$lht-white;
    background-color: variables.$lht-blue-100;

    .description,
    .bottom-text {
      font-size: 0.75rem;
    }

    .icon {
      width: 60px;
      height: 60px;
    }

    .default > h4 {
      font-size: 1rem;
    }
  }
}
