<div class="box-wrapper" [ngClass]="item.isBackgroundDark ? 'bg-dark' : ''">
  <div class="default">
    <div [ngClass]="item.cssClass"></div>
    <h4>{{item.title | translate}}</h4>
  </div>
  <div class="hover">
    <p class="description">{{item.description || '' | translate}}</p>
    <div class="bottom-text">
      <p>{{'common.goTo' | translate}} {{item.goToText || item.title | translate}}</p>
      <span class="icon icon-arrow-right"></span>
    </div>
  </div>
</div>
