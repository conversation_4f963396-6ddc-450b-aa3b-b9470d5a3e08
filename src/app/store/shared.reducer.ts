import { createReducer, on } from '@ngrx/store';

import { UserDetails } from '@openapi/models/user-details';

import { setHasUnsavedChanges, setMainSidebarState, setUserDetails } from './shared.actions';

export interface SharedState {
  userDetails: UserDetails | null;
  hasUnsavedChanges: boolean;
  isMainSidebarShown: boolean;
}

export const initialUserState: SharedState = {
  userDetails: null,
  hasUnsavedChanges: false,
  isMainSidebarShown: false,
};

export const sharedReducer = createReducer(
  initialUserState,
  on(setUserDetails, (state, { userDetails }) => {
    return {
      ...state,
      userDetails,
    };
  }),

  on(setHasUnsavedChanges, (state, { hasUnsavedChanges }) => {
    return {
      ...state,
      hasUnsavedChanges: hasUnsavedChanges,
    };
  }),
  on(setMainSidebarState, (state, { isMainSidebarShown }) => {
    return {
      ...state,
      isMainSidebarShown,
    };
  }),
);
