import { createFeatureSelector, createSelector } from '@ngrx/store';

import { SharedState } from './shared.reducer';

export const selectSharedState = createFeatureSelector<SharedState>('shared');

export const loggedUserName = createSelector(selectSharedState, (state: SharedState) => state.userDetails?.name);

export const userDetails = createSelector(selectSharedState, (state: SharedState) => state.userDetails);

export const isMainSidebarShown = createSelector(selectSharedState, (state: SharedState) => state.isMainSidebarShown);

export const userRole = createSelector(selectSharedState, (state: SharedState) => state.userDetails?.role);

export const selectHasUnsavedChanges = createSelector(
  selectSharedState,
  (state: SharedState) => state.hasUnsavedChanges,
);
