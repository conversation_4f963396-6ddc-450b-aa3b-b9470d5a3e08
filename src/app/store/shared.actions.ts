import { createAction, props } from '@ngrx/store';

import { UserDetails } from '@openapi/models/user-details';

const SHARED_STATE = 'Shared';

export const getUserDetails = createAction(`[${SHARED_STATE}] Get user details`);
export const setUserDetails = createAction(
  `[${SHARED_STATE}] Set user details`,
  props<{ userDetails: UserDetails | null }>(),
);

export const setMainSidebarState = createAction(
  `[${SHARED_STATE}] Set is main sidebar shown`,
  props<{ isMainSidebarShown: boolean }>(),
);

export const setHasUnsavedChanges = createAction(
  `[${SHARED_STATE}] Set hasUnsavedChanges`,
  props<{ hasUnsavedChanges: boolean }>(),
);

export const discardChanges = createAction(`[${SHARED_STATE}] Discard changes clicked.`);
