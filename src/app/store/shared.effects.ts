import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, switchMap } from 'rxjs';

import { UserService } from '@openapi/services';

import { getUserDetails, setUserDetails } from './shared.actions';

@Injectable()
export class SharedEffects {
  constructor(
    private actions$: Actions,
    private userService: UserService,
  ) {}

  getUserDetails$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getUserDetails),
      switchMap(() => {
        return this.userService.getOrCreateUserDetails().pipe(
          map(userDetails => {
            return setUserDetails({ userDetails: userDetails.data || null });
          }),
        );
      }),
    );
  });
}
