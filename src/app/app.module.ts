import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';

import { CoreModule } from '@core/core.module';
import { AppRoutingModule } from './app-routing.module';
import { HomeModule } from './modules/home/<USER>';

import { AppComponent } from './app.component';

import { SharedEffects } from './store/shared.effects';
import { sharedReducer } from './store/shared.reducer';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    CoreModule,
    HomeModule,
    StoreModule.forRoot({
      shared: sharedReducer,
    }),
    EffectsModule.forRoot([SharedEffects]),
    StoreDevtoolsModule.instrument({
      maxAge: 25,
      logOnly: false,
      connectInZone: true,
    }),
  ],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}
