{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"coca-ui-host-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/coca-ui-host-app", "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/scss/bootstrap.scss", "@lufthansatechnik/bootstrap5/build/index.min.css", "src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/assets/styles"]}, "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "main": "src/main.ts", "extraWebpackConfig": "webpack.config.js", "commonChunk": false}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "15kb"}], "outputHashing": "all", "extraWebpackConfig": "webpack.prod.config.js"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"buildTarget": "coca-ui-host-app:build:production", "extraWebpackConfig": "webpack.prod.config.js"}, "development": {"buildTarget": "coca-ui-host-app:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4200, "publicHost": "http://localhost:4200", "extraWebpackConfig": "webpack.config.js"}}, "extract-i18n": {"builder": "ngx-build-plus:extract-i18n", "options": {"buildTarget": "coca-ui-host-app:build", "extraWebpackConfig": "webpack.config.js"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}}