const { shareAll, withModuleFederationPlugin } = require('@angular-architects/module-federation/webpack');

module.exports = withModuleFederationPlugin({
  name: 'host',
  filename: 'remoteEntry.js',
  exposes: {
    //Shared services
    './UnsavedChangesService': './src/app/shared/services/unsaved-changes/unsaved-changes.service.ts',
    './NavigationToErrorPageService':
      './src/app/shared/services/navigation-service/navigation-to-error-page.service.ts',
    './ToastService': './src/app/shared/services/toast-service/toast.service.ts',
    './LoadingWrapperService': './src/app/core/services/loading-wrapper-service/loading-wrapper.service.ts',
    './CurrentUserService': './src/app/core/services/current-user-service/current-user.service.ts',
    './AuthService': './src/app/core/services/auth-service/auth.service.ts',
    //Shared components
    './BreadcrumbComponent': './src/app/shared/components/breadcrumb/breadcrumb.component.ts',
  },
  remotes: {},
  shared: {
    ...shareAll({
      singleton: true,
      strictVersion: false,
      requiredVersion: 'auto',
    }),
  },
});
