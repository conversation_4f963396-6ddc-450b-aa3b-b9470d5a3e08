global:
  stage: test

configmap:
  production: false
  baseApiPath: https://core-calculation-service-test.apps.az.lhtcloud.com/api
  keycloakBaseUrl: https://sso-kons.app.lhtcloud.com/auth
  keycloakRedirectUri: https://core-calculation-test.apps.az.lhtcloud.com
  mfe-coca-pricing-tool: "https://core-calculation-pricing-tool-test.apps.az.lhtcloud.com"
  mfe-coca-admin-panel: "https://core-calculation-admin-panel-test.apps.az.lhtcloud.com"
  mfe-coca-project-list-ui: "https://core-calculation-project-list-ui-test.apps.az.lhtcloud.com"
  mfe-coca-cost-module-frontend: "https://core-calculation-cost-module-test.apps.az.lhtcloud.com"

lht-platform-nginx:
  base-config:
    buildConfig:
      include: false

    imageStream:
      include: false

    deploymentConfig:
      volumeMounts:
        - mountPath: /opt/app-root/src/assets/config
          name: config
          readOnly: true
        - mountPath: /opt/app-root/src/assets/config-maintenance
          name: config-maintenance
          readOnly: true

      volumes:
        - name: config
          configMap:
            defaultMode: 420
            name: coca-ui-host-app-test
        - name: config-maintenance
          configMap:
            defaultMode: 420
            name: coca-ui-host-app-test-maintenance

    route:
      prefix: core-calculation-test
