global:
  name: coca-ui-host-app
  appId: 2938
  host: azureTI
  managedBy: Core Calculation Team

configmap:
  keycloakClientId: core_calculation
  keycloakRealm: LHT

lht-platform-nginx:
  base-config:
    buildConfig:
      outputImageTag: latest

    deploymentConfig:
      podAntiAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                    - '{{ include "app.fullname" . }}'
            topologyKey: kubernetes.io/hostname

      resources:
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
