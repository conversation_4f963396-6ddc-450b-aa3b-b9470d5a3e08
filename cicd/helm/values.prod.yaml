global:
  stage: prod

configmap:
  production: true
  baseApiPath: https://core-calculation-service.apps.az.lhtcloud.com/api
  keycloakBaseUrl: https://sso.app.lhtcloud.com/auth
  keycloakRedirectUri: https://core-calculation.apps.az.lhtcloud.com
  mfe-coca-pricing-tool: "https://core-calculation-pricing-tool.apps.az.lhtcloud.com"
  mfe-coca-admin-panel: "https://core-calculation-admin-panel.apps.az.lhtcloud.com"

lht-platform-nginx:
  base-config:
    buildConfig:
      include: false

    # Enabling the pod disruption budget will prevent unwanted (but i.e. manually caused) deletions and ensures service availability
    # More information: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/
    podDisruptionBudget:
      include: true
      maxUnavailable: 1

    deploymentConfig:
      volumeMounts:
        - mountPath: /opt/app-root/src/assets/config
          name: config
          readOnly: true
        - mountPath: /opt/app-root/src/assets/config-maintenance
          name: config-maintenance
          readOnly: true

      volumes:
        - name: config
          configMap:
            defaultMode: 420
            name: coca-ui-host-app
        - name: config-maintenance
          configMap:
            defaultMode: 420
            name: coca-ui-host-app-maintenance

    route:
      prefix: core-calculation
