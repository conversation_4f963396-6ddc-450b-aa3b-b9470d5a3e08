#!groovy

pipeline {
  agent {
    label 'node20'
  }

  options {
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10'))
    disableConcurrentBuilds()
  }

  parameters {
    choice(name: 'ENVIRONMENT', choices: ['develop', 'test', 'stage', 'prod'])
    booleanParam(name: 'MAINTENANCE_MODE', defaultValue: false)
  }

  stages {
    stage("Maintenance mode") {
      steps {
        script {
          if (params.ENVIRONMENT == 'prod') {
            loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-prod', token: 'OpenShift4ProdToken')
          } else {
            loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-nonprod', token: 'OpenShift4Token')
          }

          sh """
            if [ "${params.ENVIRONMENT}" = "prod" ]
            then
              oc get cm coca-ui-host-app-maintenance -o yaml > configmap.yaml
            else
              oc get cm coca-ui-host-app-${params.ENVIRONMENT}-maintenance -o yaml > configmap.yaml
            fi

            if ${params.MAINTENANCE_MODE}
            then
              sed -i 's/"isUnderMaintenance": false/"isUnderMaintenance": true/' configmap.yaml
            else
              sed -i 's/"isUnderMaintenance": true/"isUnderMaintenance": false/' configmap.yaml
            fi

            oc apply -f configmap.yaml
          """
        }
      }
    }
    stage("Restarting pods") {
      steps {
        script {
          if (params.ENVIRONMENT == 'prod') {
            loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-prod', token: 'OpenShift4ProdToken')
          } else {
            loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-nonprod', token: 'OpenShift4Token')
          }

          sh """
            if [ "${params.ENVIRONMENT}" = "prod" ]
            then
              oc scale dc coca-ui-host-app --replicas=0
            else
              oc scale dc coca-ui-host-app-${params.ENVIRONMENT} --replicas=0
            fi

            if [ "${params.ENVIRONMENT}" = "prod" ]
            then
              oc scale dc coca-ui-host-app --replicas=2
            else
              oc scale dc coca-ui-host-app-${params.ENVIRONMENT} --replicas=1
            fi
          """
        }
      }
    }
  }

  post {
    cleanup {
      cleanWs()
    }
  }
}
