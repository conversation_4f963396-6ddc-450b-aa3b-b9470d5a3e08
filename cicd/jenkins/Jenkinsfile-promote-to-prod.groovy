#!groovy

pipeline {
  agent {
    label 'node20'
  }

  parameters {
    string(name: 'Version', defaultValue: 'Which version are we deploying ?', description: '')
  }

  options {
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10'))
    disableConcurrentBuilds()
  }

  environment {
    APP_NAME = "coca-ui-host-app"
    OC_PROJECT_PROD = "t-core-calc-prod"
    OC_PROJECT_NONPROD = "t-core-calc-nonprod"
    HELM_CHART_DIR = "cicd/helm"
    DEPLOYMENT_STAGE = "prod"
    DEPLOYMENT_CONFIG = "${APP_NAME}-${DEPLOYMENT_STAGE}"
  }

  stages {
    stage("Deploy") {
      steps {
        script {

          currentBuild.description = "${params.Version}"

          loginToOCP(cluster: 'azureTI', namespace: '${OC_PROJECT_PROD}', token: 'OpenShift4ProdToken')

          sh """
            helm upgrade --install ${DEPLOYMENT_CONFIG} ${HELM_CHART_DIR} \
              -f ${HELM_CHART_DIR}/values.yaml \
              -f ${HELM_CHART_DIR}/values.${DEPLOYMENT_STAGE}.yaml \
              --reuse-values \
              --atomic \
              --dependency-update

            oc tag ${OC_PROJECT_NONPROD}/${APP_NAME}:stage ${OC_PROJECT_PROD}/${APP_NAME}:${DEPLOYMENT_STAGE}
            oc tag ${OC_PROJECT_PROD}/${APP_NAME}:${DEPLOYMENT_STAGE} ${OC_PROJECT_PROD}/${APP_NAME}:latest

            oc rollout status deploymentconfig ${APP_NAME} --watch=true --timeout=5m
      	  """
        }
      }
    }
  }

  post {
    always {
      script {
        currentBuild.result = currentBuild.result ?: 'SUCCESS'
        notifyGitHub()
      }
    }
    cleanup { 
      cleanWs()
    }
  }
}
