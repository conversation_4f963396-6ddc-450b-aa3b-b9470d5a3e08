#!groovy

pipeline {
  agent {
    label 'node20'
  }

  options {
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10'))
    disableConcurrentBuilds()
  }

  environment {
    APP_NAME = "coca-ui-host-app"
    OC_PROJECT = "t-core-calc-nonprod"
    HELM_CHART_DIR = "cicd/helm"
    DEPLOYMENT_STAGE = "develop"
    DEPLOYMENT_CONFIG = "${APP_NAME}-${DEPLOYMENT_STAGE}"
    BRANCH = "${env.GIT_BRANCH.replace('/', '-')}"
  }

  stages {
    stage("Initialize Submodules") {
      steps {
        sshagent(['github-the-core-calculation-backend-openapi-access-key']) {
          sh '''
            git submodule set-url coca-backend-openapi **************:lht-general/the-core-calculation-backend-openapi.git
            git submodule sync --recursive
            git submodule update --init
          '''
        }
      }
    }
    stage("Build") {
      steps {
        script {
          sh """
            npm install
            npm run generate:api
            npm run build
          """
        }
      }
    }

    stage("Sonar scan") {
      steps {
        script {
          scanSonar('appID': '2938', 'pathToSource': './src')
        }
      }
    }

    stage("Snyk scan") {
      when {
        anyOf {
          branch 'main'
          branch 'develop'
          branch 'master'
        }
      }
      steps {
        script {
          scanSnyk('appID': '2938', 'scope': 'frontend', 'pathToTarget': './package.json', 'scanAuto': true, 'scanBranches': ['develop', 'main', 'master'])
        }
      }
    }

    stage("Deploy") {
      steps {
        script {
          loginToOCP(cluster: 'azureTI', namespace: '${OC_PROJECT}', token: 'OpenShift4Token')

          sh """
            helm upgrade --install ${DEPLOYMENT_CONFIG} ${HELM_CHART_DIR} \
              -f ${HELM_CHART_DIR}/values.yaml \
              -f ${HELM_CHART_DIR}/values.${DEPLOYMENT_STAGE}.yaml \
              --atomic \
              --dependency-update

            oc start-build ${APP_NAME} --from-dir=dist/coca-ui-host-app --follow --wait

            oc tag ${OC_PROJECT}/${APP_NAME}:latest ${OC_PROJECT}/${APP_NAME}:${BRANCH}
      	  """

          isManuallyTriggered = !currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause').isEmpty()

          if (env.GIT_BRANCH == 'develop' || isManuallyTriggered) {
            sh """
              oc tag ${OC_PROJECT}/${APP_NAME}:latest ${OC_PROJECT}/${APP_NAME}:${DEPLOYMENT_STAGE}

              oc rollout status deploymentconfig ${DEPLOYMENT_CONFIG} --watch=true --timeout=5m
            """
          }
        }
      }
    }
  }

  post {
    always {
      script {
        currentBuild.result = currentBuild.result ?: 'SUCCESS'
        notifyGitHub()
      }
    }
    cleanup {
      cleanWs()
    }
  }
}
