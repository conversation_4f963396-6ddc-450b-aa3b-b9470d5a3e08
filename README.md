# Why We Decided to Go with Micro Frontends (Micro FE)

## 1. Problems Micro Frontends Solve

### 1.1. Introduction

We aim to leverage the frontend to introduce improvements and solutions that enhance our workflow and can be shared with other teams.

### 1.2. Reasons for Adopting Micro Frontends

#### Simple, decoupled codebases
- Our features and code base are expanding daily. We have a Pricing Tool (PT) module, an Admin Panel, a Business Plan Tool, and potentially two more tools.
- Combining all this logic into a single place is possible but splitting implementations into isolated environments makes debugging, updating, and code understanding easier for developers. The whole maintenance of the project will be smoother.

#### Isolated Team Code / Independence of tools
- Pricing Tool, Admin Panel and all other future tools are independent of each other. For instance, a user can use the Admin Panel without needing to use the Pricing Tool.
- With the current implementation, if the build fails due to an issue with the Pricing Tool, the user cannot use the Admin Panel. Therefore, we need a solution that provides separate deployments and isolated usage.

#### Adopting different tech stack
Since micro frontends consist of small, independent pieces, each of them can be implemented using different technology stacks. This is an unbelievably great strength. The starting team can be split into many small teams based on the expertise of a particular tech stack, which also respects the single responsibility principle.

## 2. What Were the Options: Monorepo vs. Polyrepo

### 2.1. Monorepo (with tools like Nx)
  - Centralized management with a single `package.json` responsible for all dependencies within the workspace.
  - Easier to maintain consistency across the project.
  - Suitable for some cases, offering useful features for managing a single codebase.
### 2.2. Polyrepo (Module Federation)
  - Flexibility to handle dependencies individually, which is crucial for our needs - separate packages.
  - Enables the use of different UI libraries or frameworks across tools, accommodating changes like LHT's new style guide and new Angular approaches for future features.
  - Simplifies the process of incorporating new technologies without affecting existing implementations.
  - Requires careful coordination to ensure smooth data sharing between micro frontends.

  ## 3. Module Federation setup
  The real challenge in adapting this microservice architecture on the frontend is our apps still needed to be rendered on the same browser window and the whole app needs to work seamlessly like one single unit. To achieve this we need an app that could:
  - Provide a communication medium for exchanging data between apps.
  - Manage individual micro apps lifecycle. i.e, downloading, mounting on the UI, unmounting, etc.
  - Provide a mechanism to render some common UI elements that’ll be shared across the whole app. In our case, for example, the unsaved changes service and few more.
  - All of these core requirements are covered by the container app.

<img title="Micro FE architecture" alt="Micro FE architecture" src="./src/assets/images/micro-fe.png" width="600" height="400">
<br/><br/>

  ## 4. Webpack Module Federation Plugin
  Webpack is a well-known framework that is widely used in Frontend for years. It is very efficient as it minimizes the demand on the server's processing power and reduces the package size to only needed dependencies.
  The Webpack Module Federation Plugin is a groundbreaking feature introduced in Webpack 5, designed to facilitate the sharing and loading of modules between separate applications dynamically at runtime. This innovation is particularly useful for building micro frontends, enabling multiple independently deployed and developed front-end applications to coexist and interact seamlessly.
  The concept revolves around two primary roles: Host and Remote.

  **Host Application**: The main application that loads remote modules. It is responsible for specifying which modules it needs from remote applications and how to load them.

  **Remote Application**: An independently deployed application that exposes its modules for consumption by host applications. It declares the modules it shares and makes them available for use.

  The integration is made possible through a set of configurations and the use of dynamic imports, which allow modules to be fetched and executed on demand.
  <br/><br/><br/>

# Micro Frontends setup in CoreCalculation application (CoCa)

## 1. Architecture Overview
### 1.1. Host App - consists of general layout and home page, contains logic for authentication and all common functionalities
### 1.2. Remote Apps - currently implemented Pricing Tool and Admin Panel. In the future - all other tools can be micro front-ends (Business Plan Tool, etc)
<img title="COCA Micro FE architecture" alt="COCA Micro FE architecture" src="./src/assets/images/coca-micro-fe.png" width="600" height="400">
<br/><br/>

## 2. Webpack Configuration
  The library which is used in the application is @angular-architects/module-federation (https://www.npmjs.com/package/@angular-architects/module-federation). It leverages Webpack's Module Federation feature to enable dynamic module sharing between different Angular applications.
  The entry point for micro frontend architecture lays in webpack.config.ts:
  ```js
  const { shareAll, withModuleFederationPlugin } = require('@angular-architects/module-federation/webpack');

module.exports = withModuleFederationPlugin({
  name: 'host',
  filename: 'remoteEntry.js',
  exposes: {
    './UnsavedChangesService': './src/app/shared/services/unsaved-changes/unsaved-changes.service.ts',
    './NavigationToErrorPageService': './src/app/shared/services/navigation-service/navigation-to-error-page.service.ts',
    './ToastService': './src/app/shared/services/toast-service/toast.service.ts',
  },
  remotes: {},
  shared: {
    ...shareAll({
      singleton: true,
      strictVersion: false,
      requiredVersion: 'auto',
    }),
  },
});
  ```

- **name**
  The unique name for the exposed container. Module Federation uses the ContainerPlugin and when it is initialized, the name you entered will be used as the file name for the container’s relative path.
- **filename**
  Filename is used to specify the file name for the output bundle that also serves as an entry point to the bundle.
- **remotes**
  The remote option is a list of remote modules that can be accessed by the local module. Remote can be an array or an object. The object is intentionally left empty. As we are not using environment variables (with dotenv files), we don't know at build time, which will be the path to a specific remote. We need to load the remote dynamically based on the values in configurations files which are evaluated at runtime. For this purpose, we are using the exposed method from @angular-architects/module-federation - loadRemoteModule. Here is setup which can be found in route-config.service.ts:
```js
{
path: MAIN_ROUTES.pricingTool,
canActivate: [PermissionsGuard, MaintenanceGuard],
data: {
  breadcrumb: {
    title: `${this.translate.instant('common.home')}`,
    link: MAIN_ROUTES.home,
    noSidebarLayout: false,
  },
},
loadChildren: () =>
  loadRemoteModule({
    type: 'module',
    remoteEntry: `${this.configService.getRemoteAppUrl(ApplicationNames.PRICING_TOOL)}/remoteEntry.js`,
    exposedModule: './PricingTool',
  })
    .then(m => m.PricingToolModule)
    .catch(err => console.error('Error loading remote module', err)),
},
```
- **exposes**
  This is the path to the module or files exposed by the container; it can be an object or an array. Here we place everything that we want to be used inside our remotes (micro frontends)
- **shared**
  Powerful feature that leverages the SharedPlugin to manage how libraries are shared across different modules in the shared scope. This capability is crucial for optimizing dependencies and ensuring consistency across micro frontends. Some important config options to know are:
  - **singleton** - Ensures that only a single instance of the shared module exists in the shared scope. This guarantees that only one version of the package is loaded on the page at any given time, preventing potential conflicts and ensuring consistency across the application.
  - **requiredVersion** - Specifies the required version of the shared module. By defining this, you ensure that the shared module used in the application meets the version requirements, thereby avoiding issues caused by incompatible versions. By setting it to 'auto', it gets the version inside its package.json
  - **strictVersion** - Enforces version consistency by rejecting shared modules if their versions do not match the specified criteria. This is particularly useful for maintaining compatibility and preventing version mismatches that could lead to runtime errors or unexpected behavior.

    | Aspect                          | `strictVersion: true`                                    | `strictVersion: false`                            |
    |---------------------------------|----------------------------------------------------------|---------------------------------------------------|
    | **Major Version Mismatch**      | Throws an error, application does not run                | No error, application runs with available version |
    | **Minor/Patch Version Mismatch**| Logs a warning, application runs                         | No warning or error, application runs             |
    | **Use Case**                    | Ensures strict version control, suitable for avoiding breaking changes | More flexibility, suitable for environments where version discrepancies are less critical |

## 3. Run Host app
  - Clone the repo from here: https://lht.app.lufthansa.com/stash/projects/COCA/repos/coca-ui-host-app/branches
  - Checkout develop branch and run **npm install**
  - You will see that coca-backend-openapi folder is present marked as a submodule. That is, because the openapi specification lays in        another git repo. You can find its configuration inside .gitmodules file. Run **git submodule update --init** which is used to initialize, fetch, and check out the submodules in our repo.
  - Navigate to the submodule folder, checkout the master branch and run git pull.
  - Go back to the root level of the project and run **npm run generate:api** which will generate for us all models and services. They should be available now in core/openapi folder.
  - Run **npm start** and load the app on http://localhost:4200/
